import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  CheckCircle, 
  AlertTriangle, 
  Zap,
  Brain
} from 'lucide-react';
import { generateQuiz, QuizGenerationRequest } from '@/services/quizGeneratorService';
import { useUser } from '@/hooks/useAuth';

export default function QuizGeneratorTest() {
  const { data: user } = useUser();
  const [testContent, setTestContent] = useState(`
Photosynthesis is the process by which plants convert light energy into chemical energy. This process occurs in the chloroplasts of plant cells and involves two main stages: the light-dependent reactions and the Calvin cycle.

During the light-dependent reactions, chlorophyll absorbs light energy and converts it into ATP and NADPH. These energy carriers are then used in the Calvin cycle to convert carbon dioxide into glucose.

The overall equation for photosynthesis is:
6CO2 + 6H2O + light energy → C6H12O6 + 6O2

This process is essential for life on Earth as it produces oxygen and serves as the foundation of most food chains.
  `.trim());
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTestQuizGeneration = async () => {
    if (!user) {
      setError('Please log in to test quiz generation');
      return;
    }

    setIsGenerating(true);
    setResult(null);
    setError(null);

    try {
      console.log('🧪 Starting quiz generation test...');
      
      const request: QuizGenerationRequest = {
        content: testContent,
        numQuestions: 3,
        difficulty: 'medium',
        questionTypes: ['multiple-choice', 'true-false'],
        subject: 'Biology'
      };

      console.log('📝 Test request:', request);
      
      const response = await generateQuiz(request);
      
      console.log('📊 Test response:', response);
      
      if (response.success && response.quiz) {
        setResult(response);
        console.log('✅ Quiz generation test successful!');
      } else {
        setError(response.error || 'Quiz generation failed');
        console.error('❌ Quiz generation test failed:', response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('❌ Quiz generation test error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-12 h-12 text-orange-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-orange-900 mb-2">Authentication Required</h2>
              <p className="text-orange-700">Please log in to test the quiz generator.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Quiz Generator Test</h1>
          <p className="text-gray-600">Test the OpenRouter API integration for quiz generation</p>
        </div>

        {/* Test Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5 text-blue-500" />
              Test Content
            </CardTitle>
            <CardDescription>
              Sample content to generate quiz questions from
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={testContent}
              onChange={(e) => setTestContent(e.target.value)}
              rows={8}
              className="mb-4"
              placeholder="Enter content to generate quiz from..."
            />
            <Button 
              onClick={handleTestQuizGeneration}
              disabled={isGenerating || !testContent.trim()}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating Quiz...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Test Quiz Generation
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-semibold">Error:</span>
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success Result */}
        {result && result.success && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="w-5 h-5" />
                Quiz Generated Successfully!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Title</p>
                    <p className="font-semibold">{result.quiz.title}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Questions</p>
                    <p className="font-semibold">{result.quiz.questions.length}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Subject</p>
                    <p className="font-semibold">{result.quiz.metadata.subject}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Difficulty</p>
                    <Badge>{result.quiz.metadata.difficulty}</Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Sample Questions:</h4>
                  <div className="space-y-3">
                    {result.quiz.questions.slice(0, 2).map((question: any, index: number) => (
                      <div key={index} className="bg-white p-3 rounded border">
                        <p className="font-medium mb-2">{question.question}</p>
                        {question.options && (
                          <div className="space-y-1">
                            {question.options.map((option: string, optIndex: number) => (
                              <p key={optIndex} className="text-sm text-gray-600">{option}</p>
                            ))}
                          </div>
                        )}
                        <p className="text-sm text-green-600 mt-2">
                          <strong>Answer:</strong> {question.correct_answer}
                        </p>
                        {question.explanation && (
                          <p className="text-sm text-gray-500 mt-1">
                            <strong>Explanation:</strong> {question.explanation}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Raw Response Debug */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
              <CardDescription>Raw response data for debugging</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
