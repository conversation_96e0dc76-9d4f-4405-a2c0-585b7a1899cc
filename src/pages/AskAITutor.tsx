
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Send,
  Bot,
  User,
  BookOpen,
  Brain,
  Calculator,
  Atom,
  Globe,
  Palette,
  Music,
  Code,
  MessageSquare,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Loader2,
  Sparkles,
  X,
  Edit
} from "lucide-react";
import { toast } from "sonner";
import AuthGuard from "@/components/AuthGuard";
import { supabase } from "@/integrations/supabase/client";
import { useCreateNote } from "@/hooks/useNotes";
import { cleanMarkdownForDisplay, cleanMarkdownForPlainText } from "@/utils/markdownCleaner";
import FolderSelector from "@/components/notes/FolderSelector";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  subject?: string;
  liked?: boolean;
  disliked?: boolean;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  subject: string;
  createdAt: Date;
  lastUpdated: Date;
}

const subjects = [
  { value: 'general', label: 'General', icon: Brain, color: 'bg-violet-500/20 text-white' },
  { value: 'mathematics', label: 'Mathematics', icon: Calculator, color: 'bg-blue-500/20 text-white' },
  { value: 'science', label: 'Science', icon: Atom, color: 'bg-emerald-500/20 text-white' },
  { value: 'english', label: 'English', icon: BookOpen, color: 'bg-red-100 text-red-700' },
  { value: 'history', label: 'History', icon: Globe, color: 'bg-yellow-100 text-yellow-700' },
  { value: 'art', label: 'Art', icon: Palette, color: 'bg-pink-100 text-pink-700' },
  { value: 'music', label: 'Music', icon: Music, color: 'bg-indigo-100 text-indigo-700' },
  { value: 'computer-science', label: 'Computer Science', icon: Code, color: 'bg-gray-100 text-gray-700' },
];





const AskAITutor = () => {
  // State management
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>({
    id: 'new',
    title: 'New Conversation',
    messages: [],
    subject: 'general',
    createdAt: new Date(),
    lastUpdated: new Date()
  });
  const [message, setMessage] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("general");
  const [isLoading, setIsLoading] = useState(false);

  // Save to Notes state
  const [showFolderSelector, setShowFolderSelector] = useState(false);
  const [messageToSave, setMessageToSave] = useState<Message | null>(null);

  // Hooks for notes functionality
  const createNoteMutation = useCreateNote();

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (currentConversation?.messages?.length) {
      scrollToBottom();
    }
  }, [currentConversation?.messages]);

  // Load conversations from localStorage on mount
  useEffect(() => {
    const savedConversations = localStorage.getItem('ai-tutor-conversations');
    if (savedConversations) {
      const parsed = JSON.parse(savedConversations);
      const conversationsWithDates = parsed.map((conv: any) => ({
        ...conv,
        createdAt: new Date(conv.createdAt),
        lastUpdated: new Date(conv.lastUpdated),
        messages: conv.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }));
      setConversations(conversationsWithDates);
    }
  }, []);

  // Save conversations to localStorage
  const saveConversations = (convs: Conversation[]) => {
    localStorage.setItem('ai-tutor-conversations', JSON.stringify(convs));
    setConversations(convs);
  };

  // Create new conversation
  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: `conv-${Date.now()}`,
      title: 'New Conversation',
      messages: [],
      subject: selectedSubject,
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    const updatedConversations = [newConversation, ...conversations];
    saveConversations(updatedConversations);
    setCurrentConversation(newConversation);
  };

  // Send message to OpenAI API
  const sendMessage = async () => {
    if (!message.trim()) return;

    let conversation = currentConversation;

    // Create new conversation if none exists
    if (!conversation) {
      conversation = {
        id: `conv-${Date.now()}`,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        messages: [],
        subject: selectedSubject,
        createdAt: new Date(),
        lastUpdated: new Date()
      };
    }

    // Add user message
    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date(),
      subject: selectedSubject
    };

    const updatedMessages = [...conversation.messages, userMessage];
    const updatedConversation = {
      ...conversation,
      messages: updatedMessages,
      lastUpdated: new Date(),
      title: conversation.messages.length === 0 ? message.substring(0, 50) + (message.length > 50 ? '...' : '') : conversation.title
    };

    setCurrentConversation(updatedConversation);
    setMessage("");
    setIsLoading(true);

    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      // Call our Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('ai-tutor', {
        body: {
          messages: updatedMessages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          subject: selectedSubject,
          temperature: 0.7
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to get AI response');
      }

      if (!data || !data.message) {
        throw new Error('No response received from AI tutor');
      }

      // Add assistant message
      const assistantMessage: Message = {
        id: `msg-${Date.now()}-assistant`,
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
        subject: selectedSubject
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      const finalConversation = {
        ...updatedConversation,
        messages: finalMessages,
        lastUpdated: new Date()
      };

      setCurrentConversation(finalConversation);

      // Update conversations list
      const conversationIndex = conversations.findIndex(c => c.id === finalConversation.id);
      let updatedConversations: Conversation[];

      if (conversationIndex >= 0) {
        updatedConversations = [...conversations];
        updatedConversations[conversationIndex] = finalConversation;
      } else {
        updatedConversations = [finalConversation, ...conversations];
      }

      saveConversations(updatedConversations);
      toast.success("Response received from AI tutor!");

    } catch (error) {
      console.error('Error calling AI tutor:', error);
      toast.error("Failed to get response from AI tutor. Please try again.");

      // Add error message
      const errorMessage: Message = {
        id: `msg-${Date.now()}-error`,
        role: 'assistant',
        content: "I'm sorry, I'm having trouble connecting right now. Please check your internet connection and try again, or contact support if the issue persists.",
        timestamp: new Date(),
        subject: selectedSubject
      };

      const finalMessages = [...updatedMessages, errorMessage];
      const finalConversation = {
        ...updatedConversation,
        messages: finalMessages,
        lastUpdated: new Date()
      };

      setCurrentConversation(finalConversation);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Delete conversation
  const deleteConversation = (conversationId: string) => {
    const updatedConversations = conversations.filter(c => c.id !== conversationId);
    saveConversations(updatedConversations);

    if (currentConversation?.id === conversationId) {
      setCurrentConversation(null);
    }

    toast.success("Conversation deleted");
  };

  // Copy message to clipboard
  const copyMessage = async (content: string) => {
    try {
      const cleanedContent = cleanMarkdownForPlainText(content);
      await navigator.clipboard.writeText(cleanedContent);
      toast.success("Message copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy message");
    }
  };

  // Rate message
  const rateMessage = (messageId: string, rating: 'like' | 'dislike') => {
    if (!currentConversation) return;

    const updatedMessages = currentConversation.messages.map(msg => {
      if (msg.id === messageId) {
        return {
          ...msg,
          liked: rating === 'like' ? !msg.liked : false,
          disliked: rating === 'dislike' ? !msg.disliked : false
        };
      }
      return msg;
    });

    const updatedConversation = {
      ...currentConversation,
      messages: updatedMessages
    };

    setCurrentConversation(updatedConversation);

    // Update in conversations list
    const conversationIndex = conversations.findIndex(c => c.id === currentConversation.id);
    if (conversationIndex >= 0) {
      const updatedConversations = [...conversations];
      updatedConversations[conversationIndex] = updatedConversation;
      saveConversations(updatedConversations);
    }
  };

  // Get subject info
  const getSubjectInfo = (subjectValue: string) => {
    return subjects.find(s => s.value === subjectValue) || subjects[0];
  };

  // Handle save to notes
  const handleEditAndSave = (message: Message) => {
    setMessageToSave(message);
    setShowFolderSelector(true);
  };

  // Handle folder selection and save note
  const handleFolderSelected = async (unitId: string, topicId: string) => {
    if (!messageToSave) {
      toast.error("No message to save");
      return;
    }

    try {
      const subjectInfo = getSubjectInfo(selectedSubject);
      const noteTitle = `AI Tutor: ${subjectInfo.label}`;
      const noteContent = cleanMarkdownForPlainText(messageToSave.content);

      await createNoteMutation.mutateAsync({
        title: noteTitle,
        content: noteContent,
        unit_id: unitId,
        topic_id: topicId,
        tags: ['ai-tutor', subjectInfo.label.toLowerCase(), 'conversation']
      });

      toast.success("Note saved successfully!");
      setShowFolderSelector(false);
      setMessageToSave(null);
    } catch (error) {
      console.error('Failed to save note:', error);
      toast.error("Failed to save note. Please try again.");
    }
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 relative pb-20 md:pb-0">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-4 sm:right-20 w-48 sm:w-72 h-48 sm:h-72 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-4 sm:left-20 w-48 sm:w-80 h-48 sm:h-80 bg-gradient-to-br from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2">Ask AI Tutor</h2>
              <p className="text-white/70">
                Get personalized learning assistance powered by AI. Edit responses and save them to your notes!
              </p>
            </div>
            <Button onClick={createNewConversation} className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
              <Sparkles className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          </div>

          {/* Mobile Subject Selector */}
          <div className="lg:hidden mb-4">
            <Select value={selectedSubject} onValueChange={setSelectedSubject}>
              <SelectTrigger className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {subjects.map((subject) => (
                  <SelectItem key={subject.value} value={subject.value}>
                    <div className="flex items-center gap-2">
                      <subject.icon className="w-4 h-4" />
                      {subject.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="mt-4 lg:mt-8 flex flex-col lg:grid lg:grid-cols-4 gap-3 lg:gap-6 h-[calc(100vh-180px)] lg:h-[calc(100vh-200px)]">
            {/* Sidebar - Hidden on mobile, shown on desktop */}
            <div className="hidden lg:block lg:col-span-1">
              <Card className="p-4 bg-white/10 backdrop-blur-xl border border-white/20 h-full">
                <h3 className="font-semibold text-white mb-4 flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-white/80" />
                  <span>Conversations</span>
                </h3>

                <Button
                  onClick={createNewConversation}
                  className="w-full mb-4 flex items-center gap-2"
                >
                  <Sparkles className="w-4 h-4" />
                  New Chat
                </Button>

                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {conversations.map((conv) => {
                    const subjectInfo = getSubjectInfo(conv.subject);
                    const SubjectIcon = subjectInfo.icon;

                    return (
                      <div
                        key={conv.id}
                        className={`group relative p-3 rounded-xl cursor-pointer transition-all duration-200 ${
                          currentConversation?.id === conv.id
                            ? 'bg-gradient-to-r from-violet-500/20 to-purple-500/20 border border-violet-500/30'
                            : 'bg-white/5 hover:bg-white/10 border border-transparent'
                        }`}
                        onClick={() => setCurrentConversation(conv)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            currentConversation?.id === conv.id
                              ? 'bg-gradient-to-r from-violet-500 to-purple-600'
                              : 'bg-white/10'
                          }`}>
                            <SubjectIcon className="w-4 h-4 text-white" />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="text-sm font-medium text-white truncate">
                                {conv.title}
                              </h4>
                              <Badge variant="secondary" className={`text-xs bg-white/10 text-white/80 border-white/20`}>
                                {subjectInfo.label}
                              </Badge>
                            </div>
                            <p className="text-xs text-white/60 truncate">
                              {conv.messages.length > 0
                                ? conv.messages[conv.messages.length - 1].content.substring(0, 40) + '...'
                                : 'Start a new conversation'
                              }
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-white/40">
                                {conv.messages.length} messages
                              </span>
                              <span className="text-xs text-white/40">
                                {conv.lastUpdated.toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Hover Actions */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-6 h-6 p-0 text-white/60 hover:text-white hover:bg-white/20"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversation(conv.id);
                            }}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Card>
            </div>

            {/* Main Chat Area - Full screen on mobile, white background */}
            <div className="flex-1 lg:col-span-3">
              <div className="h-full flex flex-col bg-white rounded-2xl lg:rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
                {/* Chat Header - ChatGPT Style with white background */}
                {currentConversation && (
                  <div className="px-4 lg:px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {currentConversation.title}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {getSubjectInfo(currentConversation.subject).label}
                          </p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-gray-200 text-gray-700 border-gray-300">
                        {currentConversation.messages.length} messages
                      </Badge>
                    </div>
                  </div>
                )}

                {/* Messages Area - ChatGPT Style */}
                <div className="flex-1 overflow-y-auto">
                  {currentConversation.messages.length === 0 ? (
                    <div className="h-full flex flex-col items-center justify-center text-center p-4 lg:p-8">
                      <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center mb-6">
                        <Sparkles className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-xl lg:text-2xl font-semibold text-gray-900 mb-3">How can I help you today?</h3>
                      <p className="text-gray-600 max-w-md text-base lg:text-lg">
                        Ask me anything about your studies, and I'll provide detailed explanations and guidance.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-6 lg:mt-8 max-w-2xl w-full">
                        <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                          <div className="flex items-center gap-3 mb-2">
                            <BookOpen className="w-5 h-5 text-violet-500" />
                            <span className="text-gray-900 font-medium">Explain concepts</span>
                          </div>
                          <p className="text-gray-600 text-sm">Get detailed explanations of complex topics</p>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                          <div className="flex items-center gap-3 mb-2">
                            <Calculator className="w-5 h-5 text-blue-500" />
                            <span className="text-gray-900 font-medium">Solve problems</span>
                          </div>
                          <p className="text-gray-600 text-sm">Step-by-step problem solving assistance</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-0">
                      {currentConversation.messages.map((msg) => (
                        <div
                          key={msg.id}
                          className={`group relative ${
                            msg.role === 'user'
                              ? 'bg-transparent'
                              : 'bg-gray-50/50 hover:bg-gray-50'
                          } transition-colors`}
                        >
                          <div className="max-w-4xl mx-auto px-4 lg:px-6 py-4 lg:py-6">
                            <div className="flex gap-3 lg:gap-4">
                              {/* Avatar */}
                              <div className="flex-shrink-0">
                                {msg.role === 'user' ? (
                                  <div className="w-7 h-7 lg:w-8 lg:h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                                    <User className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                                  </div>
                                ) : (
                                  <div className="w-7 h-7 lg:w-8 lg:h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <Bot className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                                  </div>
                                )}
                              </div>

                              {/* Message Content */}
                              <div className="flex-1 min-w-0">
                                <div className="text-gray-900 leading-relaxed">
                                  <div
                                    className="prose prose-gray max-w-none text-sm lg:text-base"
                                    dangerouslySetInnerHTML={{
                                      __html: cleanMarkdownForDisplay(msg.content)
                                    }}
                                  />
                                </div>

                                {/* Message Actions - ChatGPT Style */}
                                {msg.role === 'assistant' && (
                                  <div className="flex items-center gap-1 lg:gap-2 mt-3 lg:mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => copyMessage(msg.content)}
                                      className="h-7 lg:h-8 px-2 lg:px-3 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg text-xs lg:text-sm"
                                      title="Copy message"
                                    >
                                      <Copy className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                                      <span className="hidden lg:inline">Copy</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditAndSave(msg)}
                                      className="h-7 lg:h-8 px-2 lg:px-3 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg text-xs lg:text-sm"
                                      title="Edit & Save to Notes"
                                    >
                                      <Edit className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                                      <span className="hidden lg:inline">Save</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => rateMessage(msg.id, 'like')}
                                      className={`h-7 lg:h-8 px-2 lg:px-3 rounded-lg text-xs lg:text-sm ${
                                        msg.liked
                                          ? 'text-green-600 bg-green-100'
                                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                                      }`}
                                      title="Like"
                                    >
                                      <ThumbsUp className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                                      <span className="hidden lg:inline">Like</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => rateMessage(msg.id, 'dislike')}
                                      className={`h-7 lg:h-8 px-2 lg:px-3 rounded-lg text-xs lg:text-sm ${
                                        msg.disliked
                                          ? 'text-red-600 bg-red-100'
                                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                                      }`}
                                      title="Dislike"
                                    >
                                      <ThumbsDown className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                                      <span className="hidden lg:inline">Dislike</span>
                                    </Button>
                                  </div>
                                )}

                                {/* Timestamp */}
                                <div className="text-xs text-gray-400 mt-2">
                                  {msg.timestamp.toLocaleTimeString()}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Loading Message - ChatGPT Style */}
                      {isLoading && (
                        <div className="bg-gray-50/50">
                          <div className="max-w-4xl mx-auto px-4 lg:px-6 py-4 lg:py-6">
                            <div className="flex gap-3 lg:gap-4">
                              <div className="w-7 h-7 lg:w-8 lg:h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                                <Bot className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 text-gray-600">
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                  <span className="text-sm lg:text-base">AI Tutor is thinking...</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </div>

                {/* Message Input - ChatGPT Style */}
                {currentConversation && (
                  <div className="p-4 lg:p-6 border-t border-gray-200 bg-white">
                    <div className="max-w-4xl mx-auto">
                      <div className="relative">
                        <Textarea
                          ref={textareaRef}
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          onKeyDown={handleKeyDown}
                          placeholder={`Message AI Tutor about ${getSubjectInfo(selectedSubject).label.toLowerCase()}...`}
                          className="min-h-[50px] lg:min-h-[60px] max-h-[150px] lg:max-h-[200px] w-full pr-12 lg:pr-16 resize-none bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus-visible:ring-2 focus-visible:ring-violet-500 focus-visible:ring-offset-0 rounded-xl text-sm lg:text-base"
                          rows={1}
                        />
                        <Button
                          onClick={sendMessage}
                          disabled={!message.trim() || isLoading}
                          className="absolute right-2 bottom-2 w-8 h-8 lg:w-10 lg:h-10 p-0 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 rounded-lg"
                        >
                          {isLoading ? (
                            <Loader2 className="w-3 h-3 lg:w-4 lg:h-4 animate-spin" />
                          ) : (
                            <Send className="w-3 h-3 lg:w-4 lg:h-4" />
                          )}
                        </Button>
                      </div>
                      <div className="flex items-center justify-between mt-2 lg:mt-3 text-xs text-gray-500">
                        <span className="hidden lg:inline">Press Enter to send, Shift+Enter for new line</span>
                        <span className="lg:hidden">Enter to send</span>
                        <span>{message.length}/2000</span>
                      </div>
                      <div className="text-xs text-gray-400 mt-1 text-center">
                        AI Tutor can make mistakes. Consider checking important information.
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Folder Selector for Saving Notes */}
      <FolderSelector
        open={showFolderSelector}
        onClose={() => {
          setShowFolderSelector(false);
          setMessageToSave(null);
        }}
        onFolderSelected={handleFolderSelected}
        title="Save AI Response to Notes"
        description="Choose where to save this AI response in your notes."
      />
    </AuthGuard>
  );
};

export default AskAITutor;
