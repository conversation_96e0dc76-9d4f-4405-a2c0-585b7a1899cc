
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Send,
  Bot,
  User,
  BookOpen,
  Brain,
  Calculator,
  Atom,
  Globe,
  Palette,
  Music,
  Code,
  MessageSquare,
  Lightbulb,
  FileText,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Loader2,
  Sparkles,
  GraduationCap,
  HelpCircle,
  Zap,
  Target,
  Plus as PlusIcon,
  Edit,
  Save,
  Check,
  X
} from "lucide-react";
import { toast } from "sonner";
import AuthGuard from "@/components/AuthGuard";
import { supabase } from "@/integrations/supabase/client";
import { useUnits, useTopics, useCreateNote, useCreateUnit, useCreateTopic } from "@/hooks/useNotes";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cleanMarkdownForDisplay, cleanMarkdownForPlainText } from "@/utils/markdownCleaner";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  subject?: string;
  liked?: boolean;
  disliked?: boolean;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  subject: string;
  createdAt: Date;
  lastUpdated: Date;
}

const subjects = [
  { value: 'general', label: 'General', icon: Brain, color: 'bg-violet-500/20 text-white' },
  { value: 'mathematics', label: 'Mathematics', icon: Calculator, color: 'bg-blue-500/20 text-white' },
  { value: 'science', label: 'Science', icon: Atom, color: 'bg-emerald-500/20 text-white' },
  { value: 'english', label: 'English', icon: BookOpen, color: 'bg-red-100 text-red-700' },
  { value: 'history', label: 'History', icon: Globe, color: 'bg-yellow-100 text-yellow-700' },
  { value: 'art', label: 'Art', icon: Palette, color: 'bg-pink-100 text-pink-700' },
  { value: 'music', label: 'Music', icon: Music, color: 'bg-indigo-100 text-indigo-700' },
  { value: 'computer-science', label: 'Computer Science', icon: Code, color: 'bg-gray-100 text-gray-700' },
];





const AskAITutor = () => {
  // State management
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>({
    id: 'new',
    title: 'New Conversation',
    messages: [],
    subject: 'general',
    createdAt: new Date(),
    lastUpdated: new Date()
  });
  const [message, setMessage] = useState("");
  const [selectedSubject] = useState("general");
  const [isLoading, setIsLoading] = useState(false);

  // Edit and Save to Notes state
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingMessage, setEditingMessage] = useState<Message | null>(null);
  const [editedContent, setEditedContent] = useState("");
  const [noteTitle, setNoteTitle] = useState("");
  const [selectedUnitId, setSelectedUnitId] = useState("");
  const [selectedTopicId, setSelectedTopicId] = useState("");
  const [showNewUnitInput, setShowNewUnitInput] = useState(false);
  const [showNewTopicInput, setShowNewTopicInput] = useState(false);
  const [newUnitName, setNewUnitName] = useState("");
  const [newTopicName, setNewTopicName] = useState("");

  // Hooks for notes functionality
  const { data: units = [] } = useUnits();
  const { data: topics = [] } = useTopics(selectedUnitId);
  const createNoteMutation = useCreateNote();
  const createUnitMutation = useCreateUnit();
  const createTopicMutation = useCreateTopic();

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (currentConversation?.messages?.length) {
      scrollToBottom();
    }
  }, [currentConversation?.messages]);

  // Load conversations from localStorage on mount
  useEffect(() => {
    const savedConversations = localStorage.getItem('ai-tutor-conversations');
    if (savedConversations) {
      const parsed = JSON.parse(savedConversations);
      const conversationsWithDates = parsed.map((conv: any) => ({
        ...conv,
        createdAt: new Date(conv.createdAt),
        lastUpdated: new Date(conv.lastUpdated),
        messages: conv.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }));
      setConversations(conversationsWithDates);
    }
  }, []);

  // Save conversations to localStorage
  const saveConversations = (convs: Conversation[]) => {
    localStorage.setItem('ai-tutor-conversations', JSON.stringify(convs));
    setConversations(convs);
  };

  // Create new conversation
  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: `conv-${Date.now()}`,
      title: 'New Conversation',
      messages: [],
      subject: selectedSubject,
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    const updatedConversations = [newConversation, ...conversations];
    saveConversations(updatedConversations);
    setCurrentConversation(newConversation);
  };

  // Send message to OpenAI API
  const sendMessage = async () => {
    if (!message.trim()) return;

    let conversation = currentConversation;

    // Create new conversation if none exists
    if (!conversation) {
      conversation = {
        id: `conv-${Date.now()}`,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        messages: [],
        subject: selectedSubject,
        createdAt: new Date(),
        lastUpdated: new Date()
      };
    }

    // Add user message
    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date(),
      subject: selectedSubject
    };

    const updatedMessages = [...conversation.messages, userMessage];
    const updatedConversation = {
      ...conversation,
      messages: updatedMessages,
      lastUpdated: new Date(),
      title: conversation.messages.length === 0 ? message.substring(0, 50) + (message.length > 50 ? '...' : '') : conversation.title
    };

    setCurrentConversation(updatedConversation);
    setMessage("");
    setIsLoading(true);

    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      // Call our Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('ai-tutor', {
        body: {
          messages: updatedMessages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          subject: selectedSubject,
          temperature: 0.7
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to get AI response');
      }

      if (!data || !data.message) {
        throw new Error('No response received from AI tutor');
      }

      // Add assistant message
      const assistantMessage: Message = {
        id: `msg-${Date.now()}-assistant`,
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
        subject: selectedSubject
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      const finalConversation = {
        ...updatedConversation,
        messages: finalMessages,
        lastUpdated: new Date()
      };

      setCurrentConversation(finalConversation);

      // Update conversations list
      const conversationIndex = conversations.findIndex(c => c.id === finalConversation.id);
      let updatedConversations: Conversation[];

      if (conversationIndex >= 0) {
        updatedConversations = [...conversations];
        updatedConversations[conversationIndex] = finalConversation;
      } else {
        updatedConversations = [finalConversation, ...conversations];
      }

      saveConversations(updatedConversations);
      toast.success("Response received from AI tutor!");

    } catch (error) {
      console.error('Error calling AI tutor:', error);
      toast.error("Failed to get response from AI tutor. Please try again.");

      // Add error message
      const errorMessage: Message = {
        id: `msg-${Date.now()}-error`,
        role: 'assistant',
        content: "I'm sorry, I'm having trouble connecting right now. Please check your internet connection and try again, or contact support if the issue persists.",
        timestamp: new Date(),
        subject: selectedSubject
      };

      const finalMessages = [...updatedMessages, errorMessage];
      const finalConversation = {
        ...updatedConversation,
        messages: finalMessages,
        lastUpdated: new Date()
      };

      setCurrentConversation(finalConversation);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Delete conversation
  const deleteConversation = (conversationId: string) => {
    const updatedConversations = conversations.filter(c => c.id !== conversationId);
    saveConversations(updatedConversations);

    if (currentConversation?.id === conversationId) {
      setCurrentConversation(null);
    }

    toast.success("Conversation deleted");
  };

  // Copy message to clipboard
  const copyMessage = async (content: string) => {
    try {
      const cleanedContent = cleanMarkdownForPlainText(content);
      await navigator.clipboard.writeText(cleanedContent);
      toast.success("Message copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy message");
    }
  };

  // Rate message
  const rateMessage = (messageId: string, rating: 'like' | 'dislike') => {
    if (!currentConversation) return;

    const updatedMessages = currentConversation.messages.map(msg => {
      if (msg.id === messageId) {
        return {
          ...msg,
          liked: rating === 'like' ? !msg.liked : false,
          disliked: rating === 'dislike' ? !msg.disliked : false
        };
      }
      return msg;
    });

    const updatedConversation = {
      ...currentConversation,
      messages: updatedMessages
    };

    setCurrentConversation(updatedConversation);

    // Update in conversations list
    const conversationIndex = conversations.findIndex(c => c.id === currentConversation.id);
    if (conversationIndex >= 0) {
      const updatedConversations = [...conversations];
      updatedConversations[conversationIndex] = updatedConversation;
      saveConversations(updatedConversations);
    }
  };

  // Get subject info
  const getSubjectInfo = (subjectValue: string) => {
    return subjects.find(s => s.value === subjectValue) || subjects[0];
  };

  // Handle edit and save to notes
  const handleEditAndSave = (message: Message) => {
    setEditingMessage(message);
    setEditedContent(cleanMarkdownForPlainText(message.content));
    setNoteTitle(`AI Tutor: ${message.subject || selectedSubject}`);
    setEditModalOpen(true);
  };

  // Create new unit
  const createNewUnit = async () => {
    if (!newUnitName.trim()) {
      toast.error("Please enter a unit name");
      return;
    }

    try {
      const newUnit = await createUnitMutation.mutateAsync({
        name: newUnitName.trim(),
        description: `Unit created from AI Tutor`,
        color: '#6366f1'
      });

      setSelectedUnitId(newUnit.id);
      setNewUnitName("");
      setShowNewUnitInput(false);
      toast.success("Unit created successfully!");
    } catch (error) {
      toast.error("Failed to create unit");
    }
  };

  // Create new topic
  const createNewTopic = async () => {
    if (!newTopicName.trim() || !selectedUnitId) {
      toast.error("Please enter a topic name and select a unit");
      return;
    }

    try {
      const newTopic = await createTopicMutation.mutateAsync({
        name: newTopicName.trim(),
        description: `Topic created from AI Tutor`,
        unit_id: selectedUnitId
      });

      setSelectedTopicId(newTopic.id);
      setNewTopicName("");
      setShowNewTopicInput(false);
      toast.success("Topic created successfully!");
    } catch (error) {
      toast.error("Failed to create topic");
    }
  };

  // Save edited content to notes
  const saveToNotes = async () => {
    if (!editingMessage || !noteTitle.trim() || !selectedUnitId || !selectedTopicId) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      await createNoteMutation.mutateAsync({
        title: noteTitle.trim(),
        content: editedContent.trim(),
        unit_id: selectedUnitId,
        topic_id: selectedTopicId,
      });

      toast.success("Note saved successfully!");
      setEditModalOpen(false);
      setEditingMessage(null);
      setEditedContent("");
      setNoteTitle("");
      setSelectedUnitId("");
      setSelectedTopicId("");
      setShowNewUnitInput(false);
      setShowNewTopicInput(false);
      setNewUnitName("");
      setNewTopicName("");
    } catch (error) {
      toast.error("Failed to save note");
    }
  };

  // Close edit modal
  const closeEditModal = () => {
    setEditModalOpen(false);
    setEditingMessage(null);
    setEditedContent("");
    setNoteTitle("");
    setSelectedUnitId("");
    setSelectedTopicId("");
    setShowNewUnitInput(false);
    setShowNewTopicInput(false);
    setNewUnitName("");
    setNewTopicName("");
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 relative pb-20 md:pb-0">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-4 sm:right-20 w-48 sm:w-72 h-48 sm:h-72 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-4 sm:left-20 w-48 sm:w-80 h-48 sm:h-80 bg-gradient-to-br from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2">Ask AI Tutor</h2>
              <p className="text-white/70">
                Get personalized learning assistance powered by AI. Edit responses and save them to your notes!
              </p>
            </div>
            <Button onClick={createNewConversation} className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
              <Sparkles className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          </div>

          <div className="mt-8 grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Card className="p-4 bg-white/10 backdrop-blur-xl border border-white/20">
                <h3 className="font-semibold text-white mb-4 flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-white/80" />
                  <span>Conversations</span>
                </h3>

                <Button
                  onClick={createNewConversation}
                  className="w-full mb-4 flex items-center gap-2"
                >
                  <Sparkles className="w-4 h-4" />
                  New Chat
                </Button>

                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {conversations.map((conv) => {
                    const subjectInfo = getSubjectInfo(conv.subject);
                    const SubjectIcon = subjectInfo.icon;

                    return (
                      <div
                        key={conv.id}
                        className={`group relative p-3 rounded-xl cursor-pointer transition-all duration-200 ${
                          currentConversation?.id === conv.id
                            ? 'bg-gradient-to-r from-violet-500/20 to-purple-500/20 border border-violet-500/30'
                            : 'bg-white/5 hover:bg-white/10 border border-transparent'
                        }`}
                        onClick={() => setCurrentConversation(conv)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            currentConversation?.id === conv.id
                              ? 'bg-gradient-to-r from-violet-500 to-purple-600'
                              : 'bg-white/10'
                          }`}>
                            <SubjectIcon className="w-4 h-4 text-white" />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="text-sm font-medium text-white truncate">
                                {conv.title}
                              </h4>
                              <Badge variant="secondary" className={`text-xs bg-white/10 text-white/80 border-white/20`}>
                                {subjectInfo.label}
                              </Badge>
                            </div>
                            <p className="text-xs text-white/60 truncate">
                              {conv.messages.length > 0
                                ? conv.messages[conv.messages.length - 1].content.substring(0, 40) + '...'
                                : 'Start a new conversation'
                              }
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-white/40">
                                {conv.messages.length} messages
                              </span>
                              <span className="text-xs text-white/40">
                                {conv.lastUpdated.toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Hover Actions */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-6 h-6 p-0 text-white/60 hover:text-white hover:bg-white/20"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversation(conv.id);
                            }}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Card>
            </div>

            {/* Main Chat Area */}
            <div className="lg:col-span-3">
              <div className="h-[calc(100vh-200px)] flex flex-col bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden">
                {/* Chat Header - ChatGPT Style */}
                {currentConversation && (
                  <div className="px-6 py-4 border-b border-white/10 bg-white/5">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">
                            {currentConversation.title}
                          </h3>
                          <p className="text-sm text-white/60">
                            {getSubjectInfo(currentConversation.subject).label}
                          </p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-white/10 text-white/80 border-white/20">
                        {currentConversation.messages.length} messages
                      </Badge>
                    </div>
                  </div>
                )}

                {/* Messages Area - ChatGPT Style */}
                <div className="flex-1 overflow-y-auto">
                  {currentConversation.messages.length === 0 ? (
                    <div className="h-full flex flex-col items-center justify-center text-center p-8">
                      <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center mb-6">
                        <Sparkles className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-semibold text-white mb-3">How can I help you today?</h3>
                      <p className="text-white/60 max-w-md text-lg">
                        Ask me anything about your studies, and I'll provide detailed explanations and guidance.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-8 max-w-2xl">
                        <div className="p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
                          <div className="flex items-center gap-3 mb-2">
                            <BookOpen className="w-5 h-5 text-violet-400" />
                            <span className="text-white font-medium">Explain concepts</span>
                          </div>
                          <p className="text-white/60 text-sm">Get detailed explanations of complex topics</p>
                        </div>
                        <div className="p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
                          <div className="flex items-center gap-3 mb-2">
                            <Calculator className="w-5 h-5 text-blue-400" />
                            <span className="text-white font-medium">Solve problems</span>
                          </div>
                          <p className="text-white/60 text-sm">Step-by-step problem solving assistance</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-0">
                      {currentConversation.messages.map((msg) => (
                        <div
                          key={msg.id}
                          className={`group relative ${
                            msg.role === 'user'
                              ? 'bg-transparent'
                              : 'bg-white/[0.02] hover:bg-white/[0.04]'
                          } transition-colors`}
                        >
                          <div className="max-w-4xl mx-auto px-6 py-6">
                            <div className="flex gap-4">
                              {/* Avatar */}
                              <div className="flex-shrink-0">
                                {msg.role === 'user' ? (
                                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                                    <User className="w-4 h-4 text-white" />
                                  </div>
                                ) : (
                                  <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <Bot className="w-4 h-4 text-white" />
                                  </div>
                                )}
                              </div>

                              {/* Message Content */}
                              <div className="flex-1 min-w-0">
                                <div className="text-white/90 leading-relaxed">
                                  <div
                                    className="prose-chatgpt max-w-none"
                                    dangerouslySetInnerHTML={{
                                      __html: cleanMarkdownForDisplay(msg.content)
                                    }}
                                  />
                                </div>

                                {/* Message Actions - ChatGPT Style */}
                                {msg.role === 'assistant' && (
                                  <div className="flex items-center gap-2 mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => copyMessage(msg.content)}
                                      className="h-8 px-3 text-white/60 hover:text-white hover:bg-white/10 rounded-lg"
                                      title="Copy message"
                                    >
                                      <Copy className="w-4 h-4 mr-1" />
                                      Copy
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditAndSave(msg)}
                                      className="h-8 px-3 text-white/60 hover:text-white hover:bg-white/10 rounded-lg"
                                      title="Edit & Save to Notes"
                                    >
                                      <Edit className="w-4 h-4 mr-1" />
                                      Edit
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => rateMessage(msg.id, 'like')}
                                      className={`h-8 px-3 rounded-lg ${
                                        msg.liked
                                          ? 'text-green-400 bg-green-400/10'
                                          : 'text-white/60 hover:text-white hover:bg-white/10'
                                      }`}
                                      title="Like"
                                    >
                                      <ThumbsUp className="w-4 h-4 mr-1" />
                                      Like
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => rateMessage(msg.id, 'dislike')}
                                      className={`h-8 px-3 rounded-lg ${
                                        msg.disliked
                                          ? 'text-red-400 bg-red-400/10'
                                          : 'text-white/60 hover:text-white hover:bg-white/10'
                                      }`}
                                      title="Dislike"
                                    >
                                      <ThumbsDown className="w-4 h-4 mr-1" />
                                      Dislike
                                    </Button>
                                  </div>
                                )}

                                {/* Timestamp */}
                                <div className="text-xs text-white/40 mt-2">
                                  {msg.timestamp.toLocaleTimeString()}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Loading Message - ChatGPT Style */}
                      {isLoading && (
                        <div className="bg-white/[0.02]">
                          <div className="max-w-4xl mx-auto px-6 py-6">
                            <div className="flex gap-4">
                              <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                                <Bot className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 text-white/70">
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                  <span>AI Tutor is thinking...</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </div>

                {/* Message Input - ChatGPT Style */}
                {currentConversation && (
                  <div className="p-6 border-t border-white/10">
                    <div className="max-w-4xl mx-auto">
                      <div className="relative">
                        <Textarea
                          ref={textareaRef}
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          onKeyDown={handleKeyDown}
                          placeholder={`Message AI Tutor about ${getSubjectInfo(selectedSubject).label.toLowerCase()}...`}
                          className="min-h-[60px] max-h-[200px] w-full pr-16 resize-none bg-white/10 border-white/20 text-white placeholder-white/50 focus-visible:ring-2 focus-visible:ring-violet-500 focus-visible:ring-offset-0 rounded-xl"
                          rows={1}
                        />
                        <Button
                          onClick={sendMessage}
                          disabled={!message.trim() || isLoading}
                          className="absolute right-2 bottom-2 w-10 h-10 p-0 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 rounded-lg"
                        >
                          {isLoading ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Send className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                      <div className="flex items-center justify-between mt-3 text-xs text-white/40">
                        <span>Press Enter to send, Shift+Enter for new line</span>
                        <span>{message.length}/2000</span>
                      </div>
                      <div className="text-xs text-white/40 mt-1 text-center">
                        AI Tutor can make mistakes. Consider checking important information.
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit and Save to Notes Modal */}
      <Dialog open={editModalOpen} onOpenChange={closeEditModal}>
        <DialogContent className="max-w-2xl bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
              <Edit className="w-5 h-5 text-blue-600" />
              Edit & Save to Notes
            </DialogTitle>
            <DialogDescription>
              Edit the AI response and save it to your organized notes.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Note Title */}
            <div>
              <Label htmlFor="note-title" className="text-sm font-medium">
                Note Title
              </Label>
              <Input
                id="note-title"
                value={noteTitle}
                onChange={(e) => setNoteTitle(e.target.value)}
                placeholder="Enter note title..."
                className="mt-1"
              />
            </div>

            {/* Unit Selection */}
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="unit-select" className="text-sm font-medium">
                  Unit
                </Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewUnitInput(!showNewUnitInput)}
                  className="h-6 text-xs"
                >
                  <PlusIcon className="w-3 h-3 mr-1" />
                  New Unit
                </Button>
              </div>

              {showNewUnitInput ? (
                <div className="flex gap-2 mt-1">
                  <Input
                    value={newUnitName}
                    onChange={(e) => setNewUnitName(e.target.value)}
                    placeholder="Enter unit name..."
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        createNewUnit();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    size="sm"
                    onClick={createNewUnit}
                    disabled={createUnitMutation.isPending || !newUnitName.trim()}
                    className="px-2"
                  >
                    {createUnitMutation.isPending ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <Check className="w-3 h-3" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowNewUnitInput(false);
                      setNewUnitName("");
                    }}
                    className="px-2"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ) : (
                <Select value={selectedUnitId} onValueChange={setSelectedUnitId}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a unit..." />
                  </SelectTrigger>
                  <SelectContent>
                    {units.length === 0 ? (
                      <div className="p-2 text-sm text-gray-500 text-center">
                        No units found. Create a new unit above.
                      </div>
                    ) : (
                      units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          {unit.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Topic Selection */}
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="topic-select" className="text-sm font-medium">
                  Topic
                </Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewTopicInput(!showNewTopicInput)}
                  disabled={!selectedUnitId}
                  className="h-6 text-xs"
                >
                  <PlusIcon className="w-3 h-3 mr-1" />
                  New Topic
                </Button>
              </div>

              {showNewTopicInput ? (
                <div className="flex gap-2 mt-1">
                  <Input
                    value={newTopicName}
                    onChange={(e) => setNewTopicName(e.target.value)}
                    placeholder="Enter topic name..."
                    className="flex-1"
                    disabled={!selectedUnitId}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        createNewTopic();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    size="sm"
                    onClick={createNewTopic}
                    disabled={createTopicMutation.isPending || !newTopicName.trim() || !selectedUnitId}
                    className="px-2"
                  >
                    {createTopicMutation.isPending ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <Check className="w-3 h-3" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowNewTopicInput(false);
                      setNewTopicName("");
                    }}
                    className="px-2"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ) : (
                <Select
                  value={selectedTopicId}
                  onValueChange={setSelectedTopicId}
                  disabled={!selectedUnitId}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a topic..." />
                  </SelectTrigger>
                  <SelectContent>
                    {topics.length === 0 ? (
                      <div className="p-2 text-sm text-gray-500 text-center">
                        {selectedUnitId ? "No topics found. Create a new topic above." : "Select a unit first."}
                      </div>
                    ) : (
                      topics.map((topic) => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Content Editor */}
            <div>
              <Label htmlFor="content-editor" className="text-sm font-medium">
                Content
              </Label>
              <Textarea
                id="content-editor"
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                placeholder="Edit the AI response..."
                className="mt-1 min-h-[200px] resize-none"
                rows={8}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={closeEditModal}
                className="flex-1"
                disabled={createNoteMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={saveToNotes}
                disabled={
                  createNoteMutation.isPending ||
                  !noteTitle.trim() ||
                  !selectedUnitId ||
                  !selectedTopicId ||
                  !editedContent.trim()
                }
                className="flex-1"
              >
                {createNoteMutation.isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save to Notes
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </AuthGuard>
  );
};

export default AskAITutor;
