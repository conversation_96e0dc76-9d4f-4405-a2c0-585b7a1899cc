import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Loader2,
  Upload,
  Download,
  Sparkles,
  Brain,
  Save,
  Copy,
  Eye,
  Settings,
  CheckCircle,
  AlertCircle,
  FileUp
} from 'lucide-react';
import { toast } from 'sonner';
import { extractTextFromFile, validateTextForQuiz, TextExtractionResult } from '@/utils/textExtractor';
import { extractTextFromPDFHybrid } from '@/services/pdfExtractionService';
import { generateQuiz, GeneratedQuiz, QuizGenerationRequest } from '@/services/quizGeneratorService';
import { exportQuizToPDF } from '@/utils/pdfExport';
import { cleanMarkdownForDisplay } from '@/utils/markdownCleaner';
import { useSubscriptionAccess } from '@/components/subscription/SubscriptionGuard';
import { useCreateNote } from '@/hooks/useNotes';
import FolderSelector from '@/components/notes/FolderSelector';

const QuizGenerator = () => {
  // Input states
  const [inputText, setInputText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [extractedText, setExtractedText] = useState('');
  const [extractionResult, setExtractionResult] = useState<TextExtractionResult | null>(null);

  // Generation states
  const [isGenerating, setIsGenerating] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [generatedQuiz, setGeneratedQuiz] = useState<GeneratedQuiz | null>(null);

  // UI states
  const [activeTab, setActiveTab] = useState<'text' | 'file'>('text');
  const [showSettings, setShowSettings] = useState(false);
  const [showFolderSelector, setShowFolderSelector] = useState(false);

  // Quiz generation settings
  const [quizSettings, setQuizSettings] = useState<QuizGenerationRequest>({
    content: '',
    numQuestions: 10,
    difficulty: 'medium',
    questionTypes: ['multiple-choice'],
    subject: 'General Study',
    includeAnswers: true
  });

  // Hooks
  const createNoteMutation = useCreateNote();
  const { hasAccess, checkAccess, SubscriptionWall } = useSubscriptionAccess('quiz generation');

  // File handling
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setExtractedText('');
      setExtractionResult(null);
    }
  };

  // Extract text from uploaded PDF file
  const handleExtractText = async () => {
    if (!selectedFile) {
      toast.error('Please select a PDF file first');
      return;
    }

    // Validate file type
    if (!selectedFile.name.toLowerCase().endsWith('.pdf')) {
      toast.error('Please select a PDF file only');
      return;
    }

    setIsExtracting(true);
    try {
      console.log('🚀 Starting advanced PDF text extraction...');
      // Use hybrid extraction for better results
      const result = await extractTextFromPDFHybrid(selectedFile);
      setExtractionResult(result);

      if (result.success && result.text) {
        setExtractedText(result.text);
        const wordCount = result.metadata?.wordCount || 0;
        const pageCount = result.metadata?.pageCount || 'unknown';
        toast.success(`PDF text extracted successfully! ${wordCount} words found from ${pageCount} pages.`);
      } else {
        console.error('PDF extraction failed:', result.error);
        toast.error(result.error || 'Failed to extract text from PDF. Please ensure the PDF contains selectable text.');
      }
    } catch (error) {
      console.error('PDF extraction error:', error);
      toast.error('An unexpected error occurred during PDF text extraction. Please try a different PDF file.');
    } finally {
      setIsExtracting(false);
    }
  };

  // Generate quiz from content
  const handleGenerateQuiz = async () => {
    // Check subscription access first
    if (!checkAccess()) {
      return;
    }

    const content = activeTab === 'text' ? inputText : extractedText;

    if (!content.trim()) {
      toast.error('Please provide content for quiz generation');
      return;
    }

    const validation = validateTextForQuiz(content);
    if (!validation.isValid) {
      toast.error(validation.error || 'Content is not suitable for quiz generation');
      return;
    }

    setIsGenerating(true);
    try {
      const request: QuizGenerationRequest = {
        ...quizSettings,
        content: validation.cleanText || content
      };

      const result = await generateQuiz(request);

      if (result.success && result.quiz) {
        setGeneratedQuiz(result.quiz);
        toast.success(`Quiz generated successfully! ${result.quiz.questions.length} questions created.`);
      } else {
        toast.error(result.error || 'Failed to generate quiz');
      }
    } catch (error) {
      console.error('Quiz generation error:', error);
      toast.error('An unexpected error occurred during quiz generation');
    } finally {
      setIsGenerating(false);
    }
  };

  // Export quiz as PDF
  const handleExportPDF = async (includeAnswers: boolean = false) => {
    if (!generatedQuiz) {
      toast.error('No quiz to export');
      return;
    }

    try {
      await exportQuizToPDF({
        title: generatedQuiz.title,
        subject: generatedQuiz.metadata.subject,
        difficulty: generatedQuiz.metadata.difficulty,
        questions: generatedQuiz.questions,
        includeAnswers,
        includeExplanations: includeAnswers,
        estimatedTime: generatedQuiz.metadata.estimatedTime
      });

      toast.success(`Quiz exported as PDF ${includeAnswers ? 'with answers' : 'without answers'}`);
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error('Failed to export quiz as PDF');
    }
  };

  // Copy quiz to clipboard
  const handleCopyQuiz = async () => {
    if (!generatedQuiz) {
      toast.error('No quiz to copy');
      return;
    }

    try {
      let quizText = `${generatedQuiz.title}\n\n`;
      quizText += `Subject: ${generatedQuiz.metadata.subject}\n`;
      quizText += `Difficulty: ${generatedQuiz.metadata.difficulty}\n`;
      quizText += `Questions: ${generatedQuiz.questions.length}\n\n`;

      generatedQuiz.questions.forEach((question, index) => {
        quizText += `${index + 1}. ${question.question}\n`;
        if (question.options) {
          question.options.forEach(option => {
            quizText += `   ${option}\n`;
          });
        }
        quizText += `   Answer: ${question.correct_answer}\n\n`;
      });

      await navigator.clipboard.writeText(quizText);
      toast.success('Quiz copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy quiz to clipboard');
    }
  };

  // Save quiz to notes
  const handleSaveToNotes = () => {
    if (!generatedQuiz) {
      toast.error('No quiz to save');
      return;
    }
    setShowFolderSelector(true);
  };

  // Handle folder selection for saving
  const handleFolderSelected = async (unitId: string, topicId: string) => {
    if (!generatedQuiz) return;

    try {
      const quizContent = cleanMarkdownForDisplay(`# ${generatedQuiz.title}

**Subject:** ${generatedQuiz.metadata.subject}
**Difficulty:** ${generatedQuiz.metadata.difficulty}
**Questions:** ${generatedQuiz.questions.length}
**Estimated Time:** ${generatedQuiz.metadata.estimatedTime} minutes

---

${generatedQuiz.questions.map((question, index) => `
## Question ${index + 1}

**${question.question}**

${question.options ? question.options.map(option => `${option}`).join('\n') : ''}

**Answer:** ${question.correct_answer}

${question.explanation ? `**Explanation:** ${question.explanation}` : ''}

---
`).join('\n')}

*Generated by StudyFam Quiz Generator*`);

      const noteData = {
        title: generatedQuiz.title,
        content: quizContent,
        unit_id: unitId,
        topic_id: topicId,
        tags: ['quiz', 'ai-generated', generatedQuiz.metadata.subject.toLowerCase()]
      };

      await createNoteMutation.mutateAsync(noteData);
      toast.success('Quiz saved to notes successfully!');
      setShowFolderSelector(false);
    } catch (error) {
      console.error('Failed to save quiz:', error);
      toast.error('Failed to save quiz to notes');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4 md:p-6 pb-20 md:pb-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI Quiz Generator
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            Generate intelligent quizzes from text, documents, or PDFs using AI
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileUp className="w-5 h-5 text-blue-600" />
                  Content Input
                </CardTitle>
                <CardDescription>
                  Provide content for quiz generation through text input or PDF upload
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'text' | 'file')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="text">Text Input</TabsTrigger>
                    <TabsTrigger value="file">PDF Upload</TabsTrigger>
                  </TabsList>

                  <TabsContent value="text" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="input-text">Paste or type your content</Label>
                      <Textarea
                        id="input-text"
                        placeholder="Paste your study material, notes, or any text content here. The AI will analyze this content and generate relevant quiz questions..."
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        className="min-h-[200px] resize-none"
                      />
                      <div className="text-sm text-gray-500">
                        {inputText.length} characters • {inputText.split(/\s+/).filter(w => w.length > 0).length} words
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="file" className="space-y-4">
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <FileUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Upload a PDF document</p>
                          <p className="text-xs text-gray-500">PDF files with selectable text are supported (max 10MB)</p>
                          <p className="text-xs text-green-600">✓ Advanced server-side extraction for better accuracy</p>
                          <p className="text-xs text-green-600">✓ Supports long documents (up to 200 pages)</p>
                          <p className="text-xs text-green-600">✓ Intelligent text positioning and formatting</p>
                          <p className="text-xs text-gray-400">✗ Image-based or scanned PDFs may not work properly</p>
                        </div>
                        <Input
                          type="file"
                          accept=".pdf"
                          onChange={handleFileChange}
                          className="mt-4"
                        />
                      </div>

                      {selectedFile && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-blue-600" />
                              <span className="font-medium">{selectedFile.name}</span>
                            </div>
                            <Badge variant="secondary">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </Badge>
                          </div>

                          <Button
                            onClick={handleExtractText}
                            disabled={isExtracting}
                            className="w-full"
                          >
                            {isExtracting ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Extracting Text...
                              </>
                            ) : (
                              <>
                                <Sparkles className="w-4 h-4 mr-2" />
                                Extract Text
                              </>
                            )}
                          </Button>
                        </div>
                      )}

                      {extractionResult && (
                        <div className={`p-4 rounded-lg ${extractionResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                          <div className="flex items-center gap-2 mb-2">
                            {extractionResult.success ? (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            ) : (
                              <AlertCircle className="w-4 h-4 text-red-600" />
                            )}
                            <span className={`font-medium ${extractionResult.success ? 'text-green-800' : 'text-red-800'}`}>
                              {extractionResult.success ? 'Text Extracted Successfully' : 'Extraction Failed'}
                            </span>
                          </div>

                          {extractionResult.success && extractionResult.metadata && (
                            <div className="text-sm text-green-700 mb-2">
                              {extractionResult.metadata.wordCount} words extracted from {extractionResult.metadata.fileType} file
                              {extractionResult.metadata.pageCount && ` (${extractionResult.metadata.pageCount} pages)`}
                            </div>
                          )}

                          {extractionResult.success && extractedText && (
                            <div className="bg-white p-3 rounded border text-xs text-gray-600 max-h-24 overflow-y-auto">
                              <strong>Preview:</strong> {extractedText.substring(0, 300)}
                              {extractedText.length > 300 && '...'}
                            </div>
                          )}

                          {!extractionResult.success && (
                            <div className="text-sm text-red-700">
                              {extractionResult.error}
                            </div>
                          )}
                        </div>
                      )}

                      {extractedText && (
                        <div className="space-y-2">
                          <Label>Extracted Content Preview</Label>
                          <div className="bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto text-sm">
                            {extractedText.substring(0, 500)}
                            {extractedText.length > 500 && '...'}
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Quiz Settings */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5 text-purple-600" />
                  Quiz Settings
                </CardTitle>
                <CardDescription>
                  Customize your quiz generation preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Input
                      id="subject"
                      placeholder="e.g., Biology, History, Math"
                      value={quizSettings.subject}
                      onChange={(e) => setQuizSettings(prev => ({ ...prev, subject: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="num-questions">Number of Questions</Label>
                    <Select
                      value={quizSettings.numQuestions?.toString()}
                      onValueChange={(value) => setQuizSettings(prev => ({ ...prev, numQuestions: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 Questions</SelectItem>
                        <SelectItem value="10">10 Questions</SelectItem>
                        <SelectItem value="15">15 Questions</SelectItem>
                        <SelectItem value="20">20 Questions</SelectItem>
                        <SelectItem value="25">25 Questions</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Difficulty Level</Label>
                  <Select
                    value={quizSettings.difficulty}
                    onValueChange={(value) => setQuizSettings(prev => ({ ...prev, difficulty: value as 'easy' | 'medium' | 'hard' }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label>Question Types</Label>
                  <div className="space-y-2">
                    {[
                      { value: 'multiple-choice', label: 'Multiple Choice' },
                      { value: 'true-false', label: 'True/False' },
                      { value: 'short-answer', label: 'Short Answer' }
                    ].map((type) => (
                      <div key={type.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={type.value}
                          checked={quizSettings.questionTypes?.includes(type.value as any)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setQuizSettings(prev => ({
                                ...prev,
                                questionTypes: [...(prev.questionTypes || []), type.value as any]
                              }));
                            } else {
                              setQuizSettings(prev => ({
                                ...prev,
                                questionTypes: prev.questionTypes?.filter(t => t !== type.value)
                              }));
                            }
                          }}
                        />
                        <Label htmlFor={type.value} className="text-sm font-normal">
                          {type.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <Button
                  onClick={handleGenerateQuiz}
                  disabled={isGenerating || (!inputText.trim() && !extractedText.trim())}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Generating Quiz...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-5 h-5 mr-2" />
                      Generate Quiz
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quiz Display Section */}
          <div className="space-y-6">
            {!generatedQuiz ? (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-12 text-center">
                  <Brain className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">No Quiz Generated Yet</h3>
                  <p className="text-gray-500">
                    Provide content and click "Generate Quiz" to create your AI-powered quiz
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
                {/* Quiz Header */}
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-2xl text-blue-600">{generatedQuiz.title}</CardTitle>
                        <CardDescription className="text-lg mt-1">
                          {generatedQuiz.metadata.subject} • {generatedQuiz.metadata.difficulty} difficulty
                        </CardDescription>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-purple-600">
                          {generatedQuiz.questions.length}
                        </div>
                        <div className="text-sm text-gray-500">Questions</div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mt-4">
                      <Badge variant="secondary">
                        {generatedQuiz.metadata.estimatedTime} min
                      </Badge>
                      <Badge variant="secondary">
                        {generatedQuiz.metadata.difficulty}
                      </Badge>
                      {generatedQuiz.questions.map(q => q.type).filter((type, index, arr) => arr.indexOf(type) === index).map(type => (
                        <Badge key={type} variant="outline">
                          {type.replace('-', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      <Button onClick={() => handleExportPDF(false)} variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export Quiz (No Answers)
                      </Button>
                      <Button onClick={() => handleExportPDF(true)} variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export with Answers
                      </Button>
                      <Button onClick={handleCopyQuiz} variant="outline" size="sm">
                        <Copy className="w-4 h-4 mr-2" />
                        Copy Quiz
                      </Button>
                      <Button onClick={handleSaveToNotes} variant="outline" size="sm">
                        <Save className="w-4 h-4 mr-2" />
                        Save to Notes
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Quiz Questions */}
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="w-5 h-5 text-green-600" />
                      Quiz Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6 max-h-96 overflow-y-auto">
                      {generatedQuiz.questions.map((question, index) => (
                        <div key={question.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                          <div className="flex items-start gap-3">
                            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0">
                              {index + 1}
                            </div>
                            <div className="flex-1 space-y-3">
                              <h4 className="font-medium text-gray-900">{question.question}</h4>

                              {question.options && (
                                <div className="space-y-1">
                                  {question.options.map((option, optionIndex) => (
                                    <div
                                      key={optionIndex}
                                      className={`p-2 rounded text-sm ${
                                        option.startsWith(question.correct_answer)
                                          ? 'bg-green-50 text-green-800 border border-green-200'
                                          : 'bg-gray-50 text-gray-700'
                                      }`}
                                    >
                                      {option}
                                    </div>
                                  ))}
                                </div>
                              )}

                              {question.type === 'short-answer' && (
                                <div className="bg-green-50 text-green-800 p-2 rounded text-sm border border-green-200">
                                  <strong>Answer:</strong> {question.correct_answer}
                                </div>
                              )}

                              {question.explanation && (
                                <div className="bg-blue-50 text-blue-800 p-2 rounded text-sm border border-blue-200">
                                  <strong>Explanation:</strong> {question.explanation}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>

        {/* Folder Selector Modal */}
        {showFolderSelector && (
          <FolderSelector
            onFolderSelected={handleFolderSelected}
            onCancel={() => setShowFolderSelector(false)}
          />
        )}

        {/* Subscription Wall */}
        <SubscriptionWall />
      </div>
    </div>
  );
};

export default QuizGenerator;
