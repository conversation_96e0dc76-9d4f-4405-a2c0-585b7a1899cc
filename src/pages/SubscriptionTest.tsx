import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  useSubscriptionStatus,
  useHasActiveAccess,
  useUserSubscription,
  useStartFreeTrial,
  useCancelSubscription
} from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
import { Loader2, Crown, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import SubscriptionStatus from '@/components/subscription/SubscriptionStatus';
import SubscriptionGuard from '@/components/subscription/SubscriptionGuard';

export default function SubscriptionTest() {
  const { data: user, isLoading: userLoading } = useUser();
  const { data: hasAccess, isLoading: accessLoading } = useHasActiveAccess();
  const { data: subscriptionStatus, isLoading: statusLoading } = useSubscriptionStatus();
  const { data: userSubscription, isLoading: subLoading } = useUserSubscription();
  
  const startTrialMutation = useStartFreeTrial();
  const cancelMutation = useCancelSubscription();

  const isLoading = userLoading || accessLoading || statusLoading || subLoading;

  const handleStartTrial = () => {
    startTrialMutation.mutate();
  };

  const handleCancel = () => {
    cancelMutation.mutate(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center p-8">
            <Loader2 className="w-8 h-8 animate-spin text-violet-500" />
            <span className="ml-2 text-gray-600">Loading subscription data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-12 h-12 text-orange-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-orange-900 mb-2">Authentication Required</h2>
              <p className="text-orange-700">Please log in to test the subscription system.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Subscription System Test</h1>
          <p className="text-gray-600">Test the StudyFam subscription functionality</p>
        </div>

        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-violet-500" />
              User Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">User ID</p>
                <p className="font-mono text-sm">{user.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="text-sm">{user.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Access Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              Access Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Badge className={hasAccess ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}>
                {hasAccess ? "✅ Has Access" : "❌ No Access"}
              </Badge>
              <p className="text-sm text-gray-600">
                {hasAccess ? "User can access premium features" : "User needs subscription"}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Subscription Status Component */}
        <Card>
          <CardHeader>
            <CardTitle>Subscription Status Component</CardTitle>
            <CardDescription>This is how the subscription status appears in the app</CardDescription>
          </CardHeader>
          <CardContent>
            <SubscriptionStatus />
          </CardContent>
        </Card>

        {/* Raw Subscription Data */}
        <Card>
          <CardHeader>
            <CardTitle>Raw Subscription Data</CardTitle>
            <CardDescription>Debug information from the database</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Subscription Status:</h4>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                  {JSON.stringify(subscriptionStatus, null, 2)}
                </pre>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">User Subscription:</h4>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                  {JSON.stringify(userSubscription, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
            <CardDescription>Test subscription functions (use with caution)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={handleStartTrial}
                disabled={startTrialMutation.isPending}
                variant="outline"
              >
                {startTrialMutation.isPending ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Clock className="w-4 h-4 mr-2" />
                )}
                Start Trial
              </Button>

              <Button
                onClick={handleCancel}
                disabled={cancelMutation.isPending}
                variant="destructive"
              >
                {cancelMutation.isPending ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <AlertTriangle className="w-4 h-4 mr-2" />
                )}
                Cancel Subscription
              </Button>
            </div>

            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-sm text-blue-700">
                <strong>Note:</strong> Payment integration has been simplified. The "Pay Now" button in the subscription wall will show a placeholder message until you integrate your preferred payment provider.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Protected Feature Test */}
        <Card>
          <CardHeader>
            <CardTitle>Protected Feature Test</CardTitle>
            <CardDescription>This content is protected by SubscriptionGuard</CardDescription>
          </CardHeader>
          <CardContent>
            <SubscriptionGuard feature="test feature">
              <div className="p-4 bg-green-50 border border-green-200 rounded">
                <CheckCircle className="w-6 h-6 text-green-500 mb-2" />
                <h3 className="font-semibold text-green-900">Premium Feature Unlocked!</h3>
                <p className="text-green-700">You have access to this premium feature.</p>
              </div>
            </SubscriptionGuard>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
