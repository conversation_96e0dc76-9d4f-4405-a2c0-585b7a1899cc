import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_cents: number;
  currency: string;
  billing_interval: string;
  billing_interval_count: number;
  trial_period_days: number;
  features: string[];
  is_active: boolean;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'trialing' | 'active' | 'past_due' | 'canceled' | 'unpaid';
  trial_start: string | null;
  trial_end: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  next_payment_due: string | null;
  last_payment_date: string | null;
  payment_method: string | null;
  payment_provider: string | null;
  canceled_at: string | null;
  cancel_at_period_end: boolean;
}

export interface SubscriptionStatus {
  subscription_id: string | null;
  status: string | null;
  trial_days_remaining: number;
  days_until_renewal: number;
  is_trial: boolean;
  plan_name: string | null;
  features: string[];
}

export interface PaymentTransaction {
  id: string;
  user_id: string;
  subscription_id: string | null;
  amount_cents: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled' | 'refunded';
  transaction_type: 'subscription' | 'trial_conversion' | 'renewal' | 'refund';
  payment_provider: string;
  payment_method: string | null;
  payment_reference: string | null;
  description: string | null;
  processed_at: string | null;
  created_at: string;
}

// Hook to get subscription plans
export function useSubscriptionPlans() {
  return useQuery({
    queryKey: ['subscription-plans'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data as SubscriptionPlan[];
    },
  });
}

// Hook to get user's subscription status
export function useSubscriptionStatus() {
  const { data: user } = useUser();

  return useQuery({
    queryKey: ['subscription-status', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('get_user_subscription_status', { p_user_id: user.id });

      if (error) throw error;
      
      if (!data || data.length === 0) {
        return {
          subscription_id: null,
          status: null,
          trial_days_remaining: 0,
          days_until_renewal: 0,
          is_trial: false,
          plan_name: null,
          features: []
        } as SubscriptionStatus;
      }

      return data[0] as SubscriptionStatus;
    },
    enabled: !!user?.id,
  });
}

// Hook to check if user has active access
export function useHasActiveAccess() {
  const { data: user } = useUser();

  return useQuery({
    queryKey: ['has-active-access', user?.id],
    queryFn: async () => {
      if (!user?.id) return false;

      const { data, error } = await supabase
        .rpc('user_has_active_access', { p_user_id: user.id });

      if (error) throw error;
      return data as boolean;
    },
    enabled: !!user?.id,
  });
}

// Hook to get user's subscription
export function useUserSubscription() {
  const { data: user } = useUser();

  return useQuery({
    queryKey: ['user-subscription', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data as UserSubscription | null;
    },
    enabled: !!user?.id,
  });
}

// Hook to get payment transactions
export function usePaymentTransactions() {
  const { data: user } = useUser();

  return useQuery({
    queryKey: ['payment-transactions', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('payment_transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as PaymentTransaction[];
    },
    enabled: !!user?.id,
  });
}

// Hook to start free trial
export function useStartFreeTrial() {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('start_free_trial', { p_user_id: user.id });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription-status'] });
      queryClient.invalidateQueries({ queryKey: ['has-active-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-subscription'] });
      toast.success('Free trial started! You have 7 days of full access.');
    },
    onError: (error) => {
      console.error('Failed to start trial:', error);
      toast.error('Failed to start trial. Please try again.');
    },
  });
}

// Hook to convert trial to paid
export function useConvertTrialToPaid() {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      payment_reference: string;
      payment_provider: string;
      payment_method: string;
    }) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('convert_trial_to_paid', {
          p_user_id: user.id,
          p_payment_reference: params.payment_reference,
          p_payment_provider: params.payment_provider,
          p_payment_method: params.payment_method,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription-status'] });
      queryClient.invalidateQueries({ queryKey: ['has-active-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-subscription'] });
      queryClient.invalidateQueries({ queryKey: ['payment-transactions'] });
      toast.success('Subscription activated! Welcome to StudyFam Pro.');
    },
    onError: (error) => {
      console.error('Failed to convert trial:', error);
      toast.error('Failed to activate subscription. Please try again.');
    },
  });
}

// Hook to renew subscription
export function useRenewSubscription() {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      payment_reference: string;
      payment_provider: string;
    }) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('renew_subscription', {
          p_user_id: user.id,
          p_payment_reference: params.payment_reference,
          p_payment_provider: params.payment_provider,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription-status'] });
      queryClient.invalidateQueries({ queryKey: ['has-active-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-subscription'] });
      queryClient.invalidateQueries({ queryKey: ['payment-transactions'] });
      toast.success('Subscription renewed successfully!');
    },
    onError: (error) => {
      console.error('Failed to renew subscription:', error);
      toast.error('Failed to renew subscription. Please try again.');
    },
  });
}

// Hook to cancel subscription
export function useCancelSubscription() {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (cancelImmediately: boolean = false) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('cancel_subscription', {
          p_user_id: user.id,
          p_cancel_immediately: cancelImmediately,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: (_, cancelImmediately) => {
      queryClient.invalidateQueries({ queryKey: ['subscription-status'] });
      queryClient.invalidateQueries({ queryKey: ['has-active-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-subscription'] });

      if (cancelImmediately) {
        toast.success('Subscription canceled immediately.');
      } else {
        toast.success('Subscription will be canceled at the end of the current period.');
      }
    },
    onError: (error) => {
      console.error('Failed to cancel subscription:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    },
  });
}
