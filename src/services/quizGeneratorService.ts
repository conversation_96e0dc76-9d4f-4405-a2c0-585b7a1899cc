import { supabase } from '@/integrations/supabase/client';

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correct_answer: string;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface GeneratedQuiz {
  title: string;
  questions: QuizQuestion[];
  metadata: {
    totalQuestions: number;
    difficulty: string;
    subject: string;
    generatedAt: string;
    estimatedTime: number;
  };
}

export interface QuizGenerationRequest {
  content: string;
  numQuestions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionTypes?: ('multiple-choice' | 'true-false' | 'short-answer')[];
  subject?: string;
  includeAnswers?: boolean;
}

export interface QuizGenerationResponse {
  success: boolean;
  quiz?: GeneratedQuiz;
  error?: string;
}

/**
 * Generate quiz using OpenRouter API via Supabase Edge Function
 */
export async function generateQuizFromContent(request: QuizGenerationRequest): Promise<QuizGenerationResponse> {
  try {
    console.log('Generating quiz with request:', request);
    console.log('Attempting to call Edge Function...');

    // Transform request to match Edge Function expected format
    const edgeFunctionRequest = {
      pdfContent: request.content, // Edge Function expects 'pdfContent'
      numQuestions: request.numQuestions || 5,
      difficulty: request.difficulty || 'medium',
      questionTypes: request.questionTypes || ['multiple-choice'],
      subject: request.subject || 'General Study'
    };

    const { data, error } = await supabase.functions.invoke('generate-quiz-from-pdf', {
      body: edgeFunctionRequest
    });

    console.log('Edge Function response:', { data, error });

    if (error) {
      console.error('Supabase function error:', error);
      // If it's a network error or function not found, throw to trigger fallback
      if (error.message?.includes('Failed to fetch') ||
          error.message?.includes('404') ||
          error.message?.includes('CORS') ||
          error.message?.includes('FunctionsFetchError')) {
        console.log('Edge Function not available, will use fallback');
        throw new Error('Edge Function not available');
      }
      return {
        success: false,
        error: error.message || 'Failed to generate quiz'
      };
    }

    // Check if we have a valid response with quiz data
    if (!data || !data.quiz || !data.quiz.questions || !Array.isArray(data.quiz.questions)) {
      console.log('Edge Function returned invalid response:', data);
      return {
        success: false,
        error: data?.error || 'Invalid quiz data received'
      };
    }

    // Transform the Edge Function response to match our expected format
    try {
      const transformedQuiz: GeneratedQuiz = {
        title: data.quiz.title || 'Generated Quiz',
        questions: data.quiz.questions.map((q: any, index: number) => ({
          id: `q${index + 1}`,
          type: q.type || 'multiple-choice',
          question: q.question || `Question ${index + 1}`,
          options: q.options || [],
          correct_answer: q.correctAnswer || q.correct_answer || 'A',
          explanation: q.explanation || 'No explanation provided',
          difficulty: request.difficulty || 'medium'
        })),
        metadata: {
          totalQuestions: data.quiz.questions.length,
          difficulty: request.difficulty || 'medium',
          subject: request.subject || 'General Study',
          generatedAt: new Date().toISOString(),
          estimatedTime: data.quiz.questions.length * 2
        }
      };

      console.log(`✅ Quiz generated successfully: "${transformedQuiz.title}" with ${transformedQuiz.questions.length} questions`);
      console.log('First question preview:', transformedQuiz.questions[0]?.question);

      return {
        success: true,
        quiz: transformedQuiz
      };
    } catch (transformError) {
      console.error('Error transforming quiz data:', transformError);
      console.log('Raw quiz data:', data.quiz);
      return {
        success: false,
        error: 'Failed to process generated quiz data'
      };
    }
  } catch (error) {
    console.error('Quiz generation error:', error);
    // Throw error to trigger fallback
    throw error;
  }
}

/**
 * Fallback quiz generation when Edge Function is not available
 */
export function generateFallbackQuiz(request: QuizGenerationRequest): QuizGenerationResponse {
  const { content, numQuestions = 5, difficulty = 'medium', subject = 'General Study', questionTypes = ['multiple-choice'] } = request;

  // Extract key terms from content for more realistic questions
  const words = content.toLowerCase().split(/\W+/).filter(word => word.length > 3);
  const keyTerms = [...new Set(words)].slice(0, 20); // Get unique terms

  const questions: QuizQuestion[] = [];

  for (let i = 1; i <= numQuestions; i++) {
    const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)];
    const keyTerm = keyTerms[Math.floor(Math.random() * keyTerms.length)] || 'concept';

    let question: QuizQuestion;

    if (questionType === 'multiple-choice') {
      question = {
        id: `q${i}`,
        type: 'multiple-choice',
        question: `What is the significance of "${keyTerm}" in the context of ${subject.toLowerCase()}?`,
        options: [
          `A) ${keyTerm} is a fundamental concept that requires further study`,
          `B) ${keyTerm} is not relevant to this topic`,
          `C) ${keyTerm} is an advanced topic beyond this scope`,
          `D) ${keyTerm} is a supporting detail in the main discussion`
        ],
        correct_answer: 'A',
        explanation: `${keyTerm} appears to be an important concept in the provided content and would benefit from further study and understanding.`,
        difficulty
      };
    } else if (questionType === 'true-false') {
      question = {
        id: `q${i}`,
        type: 'true-false',
        question: `The concept of "${keyTerm}" is discussed in the provided material.`,
        options: ['True', 'False'],
        correct_answer: 'True',
        explanation: `This term appears in the content, indicating it is relevant to the subject matter.`,
        difficulty
      };
    } else {
      question = {
        id: `q${i}`,
        type: 'short-answer',
        question: `Explain the role of "${keyTerm}" based on the provided content.`,
        correct_answer: `${keyTerm} is mentioned in the content and appears to be relevant to understanding ${subject.toLowerCase()}.`,
        explanation: `This question asks for comprehension of key concepts from the material.`,
        difficulty
      };
    }

    questions.push(question);
  }

  const quiz: GeneratedQuiz = {
    title: `${subject} Quiz - Demo Mode`,
    questions,
    metadata: {
      totalQuestions: questions.length,
      difficulty,
      subject,
      generatedAt: new Date().toISOString(),
      estimatedTime: questions.length * 2
    }
  };

  return {
    success: true,
    quiz
  };
}

/**
 * Generate quiz with fallback support
 */
export async function generateQuiz(request: QuizGenerationRequest): Promise<QuizGenerationResponse> {
  try {
    // Try the Edge Function first
    const result = await generateQuizFromContent(request);

    if (result.success) {
      return result;
    }

    // If Edge Function fails, use fallback
    console.info('Edge Function failed, using fallback quiz generator');
    const fallbackResult = generateFallbackQuiz(request);

    // Add a note about fallback mode
    if (fallbackResult.success && fallbackResult.quiz) {
      fallbackResult.quiz.title = `${fallbackResult.quiz.title} (Demo Mode)`;
    }

    return fallbackResult;

  } catch (error) {
    console.error('Quiz generation failed, using fallback:', error);
    const fallbackResult = generateFallbackQuiz(request);

    // Add a note about fallback mode
    if (fallbackResult.success && fallbackResult.quiz) {
      fallbackResult.quiz.title = `${fallbackResult.quiz.title} (Demo Mode)`;
    }

    return fallbackResult;
  }
}

/**
 * Format quiz for display
 */
export function formatQuizForDisplay(quiz: GeneratedQuiz): string {
  let formatted = `# ${quiz.title}\n\n`;
  formatted += `**Subject:** ${quiz.metadata.subject}\n`;
  formatted += `**Difficulty:** ${quiz.metadata.difficulty}\n`;
  formatted += `**Questions:** ${quiz.metadata.totalQuestions}\n`;
  formatted += `**Estimated Time:** ${quiz.metadata.estimatedTime} minutes\n\n`;
  formatted += `---\n\n`;
  
  quiz.questions.forEach((question, index) => {
    formatted += `## Question ${index + 1}\n\n`;
    formatted += `**${question.question}**\n\n`;
    
    if (question.options) {
      question.options.forEach(option => {
        formatted += `${option}\n`;
      });
    }
    
    formatted += `\n**Correct Answer:** ${question.correct_answer}\n`;
    
    if (question.explanation) {
      formatted += `**Explanation:** ${question.explanation}\n`;
    }
    
    formatted += `\n---\n\n`;
  });
  
  return formatted;
}

/**
 * Format quiz for PDF export (without answers)
 */
export function formatQuizForPDF(quiz: GeneratedQuiz, includeAnswers: boolean = false): string {
  let formatted = `${quiz.title}\n\n`;
  formatted += `Subject: ${quiz.metadata.subject}\n`;
  formatted += `Difficulty: ${quiz.metadata.difficulty}\n`;
  formatted += `Questions: ${quiz.metadata.totalQuestions}\n`;
  formatted += `Estimated Time: ${quiz.metadata.estimatedTime} minutes\n\n`;
  formatted += `${'='.repeat(50)}\n\n`;
  
  quiz.questions.forEach((question, index) => {
    formatted += `Question ${index + 1}:\n`;
    formatted += `${question.question}\n\n`;
    
    if (question.options) {
      question.options.forEach(option => {
        formatted += `${option}\n`;
      });
    }
    
    if (includeAnswers) {
      formatted += `\nCorrect Answer: ${question.correct_answer}\n`;
      
      if (question.explanation) {
        formatted += `Explanation: ${question.explanation}\n`;
      }
    }
    
    formatted += `\n${'-'.repeat(30)}\n\n`;
  });
  
  return formatted;
}

/**
 * Calculate quiz statistics
 */
export function calculateQuizStats(quiz: GeneratedQuiz) {
  const questionTypes = quiz.questions.reduce((acc, q) => {
    acc[q.type] = (acc[q.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const difficulties = quiz.questions.reduce((acc, q) => {
    acc[q.difficulty] = (acc[q.difficulty] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    totalQuestions: quiz.questions.length,
    questionTypes,
    difficulties,
    estimatedTime: quiz.metadata.estimatedTime,
    averageOptionsPerQuestion: quiz.questions
      .filter(q => q.options)
      .reduce((sum, q) => sum + (q.options?.length || 0), 0) / 
      quiz.questions.filter(q => q.options).length || 0
  };
}
