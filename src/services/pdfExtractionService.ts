import { supabase } from '@/integrations/supabase/client';

export interface PDFExtractionResult {
  success: boolean;
  text?: string;
  error?: string;
  metadata?: {
    fileName: string;
    fileSize: number;
    wordCount: number;
    extractionMethod: string;
    characterCount: number;
  };
}

/**
 * Extract text from PDF using advanced server-side processing
 */
export async function extractTextFromPDFServer(file: File): Promise<PDFExtractionResult> {
  try {
    console.log('🚀 Starting server-side PDF text extraction for:', file.name);

    // Validate file
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return {
        success: false,
        error: 'Only PDF files are supported. Please upload a PDF file.'
      };
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return {
        success: false,
        error: 'File size exceeds 10MB limit. Please upload a smaller file.'
      };
    }

    // Get current session
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return {
        success: false,
        error: 'Authentication required. Please log in to extract PDF text.'
      };
    }

    // Prepare form data
    const formData = new FormData();
    formData.append('pdf', file);

    // Call the Edge Function
    const { data, error } = await supabase.functions.invoke('extract-pdf-text', {
      body: formData,
      headers: {
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (error) {
      console.error('❌ Server-side extraction failed:', error);
      return {
        success: false,
        error: `Server extraction failed: ${error.message || 'Unknown error'}`
      };
    }

    if (!data.success) {
      console.error('❌ Extraction unsuccessful:', data.error);
      return {
        success: false,
        error: data.error || 'Failed to extract text from PDF'
      };
    }

    console.log(`✅ Server-side extraction successful: ${data.metadata.wordCount} words`);
    
    return {
      success: true,
      text: data.text,
      metadata: data.metadata
    };

  } catch (error) {
    console.error('❌ PDF extraction service error:', error);
    return {
      success: false,
      error: `Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Hybrid PDF extraction: Try server-side first, fallback to client-side
 */
export async function extractTextFromPDFHybrid(file: File): Promise<PDFExtractionResult> {
  console.log('🔄 Starting hybrid PDF extraction...');

  // Try server-side extraction first (more powerful)
  try {
    const serverResult = await extractTextFromPDFServer(file);
    
    if (serverResult.success && serverResult.text && serverResult.text.length > 100) {
      console.log('✅ Server-side extraction successful');
      return serverResult;
    } else {
      console.log('⚠️ Server-side extraction insufficient, trying client-side...');
    }
  } catch (serverError) {
    console.warn('⚠️ Server-side extraction failed, trying client-side:', serverError);
  }

  // Fallback to client-side extraction
  try {
    const { extractTextFromFile } = await import('@/utils/textExtractor');
    const clientResult = await extractTextFromFile(file);
    
    if (clientResult.success) {
      console.log('✅ Client-side extraction successful');
      return {
        success: true,
        text: clientResult.text,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          wordCount: clientResult.metadata?.wordCount || 0,
          extractionMethod: 'Client-side ' + (clientResult.metadata?.extractionMethod || 'Unknown'),
          characterCount: clientResult.text?.length || 0
        }
      };
    } else {
      return {
        success: false,
        error: clientResult.error || 'Both server-side and client-side extraction failed'
      };
    }
  } catch (clientError) {
    console.error('❌ Client-side extraction also failed:', clientError);
    return {
      success: false,
      error: 'All extraction methods failed. The PDF may be corrupted, password-protected, or contain only images.'
    };
  }
}
