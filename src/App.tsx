import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
// import { TooltipProvider } from "@/components/ui/tooltip"; // Temporarily disabled
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Outlet } from "react-router-dom";
import React from "react";
// Temporarily disabled to fix React hooks error
// import { useAuthStateChange, useUser } from "@/hooks/useAuth";
// import { useTimetableNotifications } from "@/hooks/useTimetableNotifications";
import { supabase } from "@/integrations/supabase/client";
import ErrorBoundary from "@/components/ErrorBoundary";
import { initializeAuth } from "@/utils/authUtils";
import { register as registerSW } from "@/utils/serviceWorker";
import Index from "./pages/Index";
import UploadFiles from "./pages/UploadFiles";
import SortNotes from "./pages/SortNotes";
import RevisionPlanner from "./pages/RevisionPlanner";
import AskAITutor from "./pages/AskAITutor";
import ImageToNotes from "./pages/ImageToNotes";
import StudyGroups from "./pages/StudyGroups";
import StudyGroupsList from "./pages/StudyGroupsList";
import StudyGroupDetail from "./pages/StudyGroupDetail";
import PastPapers from "./pages/PastPapers";
import TakeNotes from "./pages/TakeNotes";
import Library from "./pages/Library";
import Progress from "./pages/Progress";
import NotFound from "./pages/NotFound";
import MobileBottomNav from "./components/MobileBottomNav";
import FloatingContactButton from "./components/FloatingContactButton";
import { Navigation } from "./components/Navigation";
import Header from "./components/Header";
import Search from "./pages/Search";
import Profile from "./pages/Profile";
import Discover from "./pages/Discover";
import LandingPage from "./pages/LandingPage";
import Register from "./pages/Register";
import Login from "./pages/Login";
import AuthTest from "./pages/AuthTest";
import ContactUs from "./pages/ContactUs";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import AboutUs from "./pages/AboutUs";
import GroupInvite from "./pages/GroupInvite";
import DocumentViewerPage from "./pages/DocumentViewerPage";
import QuizGenerator from "./pages/QuizGenerator";
import Messages from "./pages/Messages";
import ReadingTimetable from "./pages/ReadingTimetable";
import Notifications from "./pages/Notifications";
import AINotes from "./pages/AINotes";
import AuthGuard from "./components/AuthGuard";
import AuthRedirect from "./components/AuthRedirect";
import Preloader from "./components/Preloader";
import SimplePreloader from "./components/SimplePreloader";
import { usePreloader } from "./hooks/usePreloader";
// import { SecurityProvider } from "./contexts/SecurityContext"; // Temporarily disabled
import PWAInstallPrompt from "./components/PWAInstallPrompt";
import OfflineDashboard from "./components/OfflineDashboard";
import OfflineDebug from "./components/OfflineDebug";

// Removed payment-related imports

const queryClient = new QueryClient();

// Main dashboard layout component
function DashboardLayout() {
  return (
    <AuthGuard>
      <div>
        <Header />
        {/* Removed Navigation here, now handled by Header */}
        <main className="flex-1">
          <div className="min-h-screen bg-[#fafbff]">
            <Outlet />
            <FloatingContactButton />
            <MobileBottomNav />
          </div>
        </main>
      </div>
    </AuthGuard>
  );
}

// Import the new layout
import StudyGroupsLayout from "./components/StudyGroups/StudyGroupsLayout";



// Auth provider component to manage auth state
function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isLoading, hidePreloader } = usePreloader({
    minDuration: 2000,
    dependencies: [],
    autoHide: true
  });

  // Initialize auth and service worker on app start
  React.useEffect(() => {
    const initializeApp = async () => {
      try {
        await initializeAuth();
        // Let the preloader handle the timing
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        hidePreloader();
      }
    };

    initializeApp();

    // Register service worker for push notifications (only in browser and production)
    if (typeof window !== 'undefined' && import.meta.env.PROD) {
      registerSW({
        onSuccess: (registration) => {
          console.log('Service Worker registered successfully:', registration);
        },
        onUpdate: (registration) => {
          console.log('Service Worker updated:', registration);
        },
      });
    }
  }, [hidePreloader]);

  return (
    <>
      {isLoading && <Preloader onComplete={hidePreloader} />}
      {children}
    </>
  );
}

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {/* Temporarily disable SecurityProvider and TooltipProvider to fix React hooks error */}
        {/* <SecurityProvider> */}
        {/* <TooltipProvider> */}
          <Toaster />
          <Sonner />
          <PWAInstallPrompt />
            <BrowserRouter>
              <AuthRedirect>
                <Routes>
                  {/* Public landing page */}
                  <Route path="/" element={<LandingPage />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/auth-test" element={<AuthTest />} />
                  <Route path="/contact-us" element={<ContactUs />} />
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  <Route path="/about-us" element={<AboutUs />} />
                  <Route path="/invite/group/:groupId" element={<GroupInvite />} />

                {/* Main app is gated at /dashboard - now using proper layout and Outlet */}
                <Route element={<DashboardLayout />}>
                  <Route path="/dashboard" element={<Index />} />
                  <Route path="/upload-files" element={<UploadFiles />} />
                  <Route path="/sort-notes" element={<SortNotes />} />
                  <Route path="/document/:noteId" element={<DocumentViewerPage />} />
                  <Route path="/revision-planner" element={<RevisionPlanner />} />
                  <Route path="/ask-ai-tutor" element={<AskAITutor />} />
                  <Route path="/quiz-generator" element={<QuizGenerator />} />
                  <Route path="/image-to-notes" element={<ImageToNotes />} />
                  {/* Group related routes with context provider layout */}
                  <Route path="/study-groups" element={<StudyGroupsLayout />}>
                    <Route index element={<StudyGroupsList />} />
                    <Route path=":groupId" element={<StudyGroupDetail />} />
                  </Route>
                  <Route path="/past-papers" element={<PastPapers />} />
                  <Route path="/take-notes" element={<TakeNotes />} />
                  <Route path="/library" element={<Library />} />
                  <Route path="/progress" element={<Progress />} />
                  <Route path="/search" element={<Search />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/discover" element={<Discover />} />
                  <Route path="/messages" element={<Messages />} />
                  <Route path="/reading-timetable" element={<ReadingTimetable />} />
                  <Route path="/notifications" element={<Notifications />} />
                  <Route path="/ai-notes" element={<AINotes />} />
                  
                  <Route path="/offline" element={<OfflineDashboard />} />
                  <Route path="/offline-debug" element={<OfflineDebug />} />
                  <Route path="*" element={<NotFound />} />
                </Route>
              </Routes>
              </AuthRedirect>
            </BrowserRouter>
        {/* </TooltipProvider> */}
        {/* </SecurityProvider> */}
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
);

export default App;
