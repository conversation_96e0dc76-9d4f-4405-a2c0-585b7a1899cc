import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Crown, 
  Clock, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { useSubscriptionStatus } from '@/hooks/useSubscription';
import SubscriptionWall from './SubscriptionWall';

interface SubscriptionStatusProps {
  showUpgradeButton?: boolean;
  compact?: boolean;
}

export default function SubscriptionStatus({ 
  showUpgradeButton = true,
  compact = false 
}: SubscriptionStatusProps) {
  const { data: status, isLoading } = useSubscriptionStatus();
  const [showWall, setShowWall] = useState(false);

  if (isLoading) {
    return (
      <Card className="animate-pulse">
        <CardContent className="p-4">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </CardContent>
      </Card>
    );
  }

  if (!status || !status.subscription_id) {
    return (
      <>
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                <div>
                  <p className="font-medium text-orange-900">No Active Subscription</p>
                  {!compact && (
                    <p className="text-sm text-orange-700">Start your free trial to access all features</p>
                  )}
                </div>
              </div>
              {showUpgradeButton && (
                <Button 
                  size="sm" 
                  onClick={() => setShowWall(true)}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Start Trial
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <SubscriptionWall
          isOpen={showWall}
          onClose={() => setShowWall(false)}
          title="Start Your Free Trial"
          description="Get 7 days of full access to all StudyFam features"
        />
      </>
    );
  }

  const getStatusInfo = () => {
    if (status.is_trial) {
      if (status.trial_days_remaining > 0) {
        return {
          icon: <Clock className="w-5 h-5 text-blue-500" />,
          title: "Free Trial Active",
          description: `${status.trial_days_remaining} days remaining`,
          badgeColor: "bg-blue-100 text-blue-700 border-blue-200",
          cardColor: "border-blue-200 bg-blue-50"
        };
      } else {
        return {
          icon: <AlertTriangle className="w-5 h-5 text-red-500" />,
          title: "Trial Expired",
          description: "Upgrade to continue using all features",
          badgeColor: "bg-red-100 text-red-700 border-red-200",
          cardColor: "border-red-200 bg-red-50"
        };
      }
    } else if (status.status === 'active') {
      return {
        icon: <Crown className="w-5 h-5 text-violet-500" />,
        title: "StudyFam Pro",
        description: status.days_until_renewal > 0 
          ? `Renews in ${status.days_until_renewal} days`
          : "Active subscription",
        badgeColor: "bg-violet-100 text-violet-700 border-violet-200",
        cardColor: "border-violet-200 bg-violet-50"
      };
    } else if (status.status === 'past_due') {
      return {
        icon: <AlertTriangle className="w-5 h-5 text-orange-500" />,
        title: "Payment Due",
        description: "Please update your payment method",
        badgeColor: "bg-orange-100 text-orange-700 border-orange-200",
        cardColor: "border-orange-200 bg-orange-50"
      };
    } else {
      return {
        icon: <AlertTriangle className="w-5 h-5 text-gray-500" />,
        title: "Subscription Inactive",
        description: "Reactivate to continue using features",
        badgeColor: "bg-gray-100 text-gray-700 border-gray-200",
        cardColor: "border-gray-200 bg-gray-50"
      };
    }
  };

  const statusInfo = getStatusInfo();
  const needsUpgrade = status.is_trial && status.trial_days_remaining <= 0;
  const needsPayment = status.status === 'past_due';

  return (
    <>
      <Card className={statusInfo.cardColor}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {statusInfo.icon}
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium text-gray-900">{statusInfo.title}</p>
                  {status.status === 'active' && (
                    <Badge className={statusInfo.badgeColor}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Active
                    </Badge>
                  )}
                  {status.is_trial && status.trial_days_remaining > 0 && (
                    <Badge className={statusInfo.badgeColor}>
                      Trial
                    </Badge>
                  )}
                </div>
                {!compact && (
                  <p className="text-sm text-gray-600">{statusInfo.description}</p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {(needsUpgrade || needsPayment) && showUpgradeButton && (
                <Button 
                  size="sm" 
                  onClick={() => setShowWall(true)}
                  className="bg-violet-500 hover:bg-violet-600"
                >
                  {needsUpgrade ? 'Upgrade' : 'Renew'}
                </Button>
              )}
              
              {!compact && status.status === 'active' && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowWall(true)}
                >
                  <Settings className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Trial Warning */}
          {status.is_trial && status.trial_days_remaining <= 3 && status.trial_days_remaining > 0 && (
            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
              <div className="flex items-center gap-2 text-yellow-800">
                <Clock className="w-4 h-4" />
                <span>
                  Your trial expires in {status.trial_days_remaining} day{status.trial_days_remaining !== 1 ? 's' : ''}. 
                  Upgrade now to continue enjoying all features.
                </span>
              </div>
            </div>
          )}

          {/* Features List (for active subscriptions) */}
          {!compact && status.status === 'active' && status.features && status.features.length > 0 && (
            <div className="mt-3 pt-3 border-t border-violet-200">
              <p className="text-xs text-gray-600 mb-2">Active Features:</p>
              <div className="flex flex-wrap gap-1">
                {status.features.slice(0, 3).map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                ))}
                {status.features.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{status.features.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <SubscriptionWall
        isOpen={showWall}
        onClose={() => setShowWall(false)}
        title={needsUpgrade ? "Upgrade to Pro" : needsPayment ? "Renew Subscription" : "Manage Subscription"}
        description={needsUpgrade ? "Continue with unlimited access" : needsPayment ? "Update payment to continue" : "Manage your subscription settings"}
      />
    </>
  );
}
