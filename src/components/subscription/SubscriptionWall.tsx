
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Crown,
  Check,
  Clock,
  Star,
  Zap,
  Shield,
  Sparkles
} from 'lucide-react';
import { useSubscriptionStatus } from '@/hooks/useSubscription';
import { toast } from 'sonner';

interface SubscriptionWallProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  feature?: string;
}

export default function SubscriptionWall({
  isOpen,
  onClose,
  title = "Upgrade to StudyFam Pro",
  description = "Continue enjoying unlimited access to all features",
  feature = "this feature"
}: SubscriptionWallProps) {
  const { data: subscriptionStatus } = useSubscriptionStatus();

  const isTrialExpired = subscriptionStatus?.is_trial && subscriptionStatus?.trial_days_remaining <= 0;
  const isSubscriptionExpired = subscriptionStatus?.status === 'past_due' || subscriptionStatus?.status === 'canceled';

  const features = [
    "Unlimited Quiz Generation",
    "AI Tutor Access",
    "Advanced PDF Extraction",
    "Export to PDF",
    "Priority Support",
    "No Ads",
    "Offline Access",
    "Advanced Analytics"
  ];

  const handlePayNow = () => {
    // TODO: Integrate payment system here
    toast.info('Payment integration coming soon! This will redirect to payment gateway.');
    console.log('Payment button clicked - integrate payment system here');
  };

  const getWallContent = () => {
    if (isTrialExpired) {
      return {
        title: "Your Free Trial Has Ended",
        description: "Continue with StudyFam Pro for just $4/month",
        buttonText: "Subscribe Now - $4/month",
        urgency: "Your 7-day free trial has expired"
      };
    } else if (isSubscriptionExpired) {
      return {
        title: "Renew Your Subscription",
        description: "Reactivate your StudyFam Pro subscription",
        buttonText: "Renew Subscription - $4/month",
        urgency: "Your subscription has expired"
      };
    } else {
      return {
        title,
        description,
        buttonText: "Upgrade to Pro - $4/month",
        urgency: `Upgrade to continue using ${feature}`
      };
    }
  };

  const content = getWallContent();

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl bg-gradient-to-br from-violet-50 to-purple-50 border-violet-200">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
              <Crown className="w-8 h-8 text-white" />
            </div>
            
            <div>
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                {content.title}
              </DialogTitle>
              <p className="text-gray-600 mt-2">{content.description}</p>
            </div>

            {(isTrialExpired || isSubscriptionExpired) && (
              <Badge variant="destructive" className="mx-auto">
                <Clock className="w-4 h-4 mr-1" />
                {content.urgency}
              </Badge>
            )}
          </DialogHeader>

          <div className="space-y-6">
            {/* Pricing Card */}
            <Card className="border-violet-200 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center gap-2">
                  <Star className="w-5 h-5 text-yellow-500 fill-current" />
                  <CardTitle className="text-xl">StudyFam Pro</CardTitle>
                  <Star className="w-5 h-5 text-yellow-500 fill-current" />
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="text-3xl font-bold text-violet-600">$4</span>
                  <span className="text-gray-500">/month</span>
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    Best Value
                  </Badge>
                </div>
                <CardDescription>
                  Billed monthly • Cancel anytime • No hidden fees
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Button */}
            <div className="space-y-3">
              <Button
                onClick={handlePayNow}
                className="w-full h-12 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white font-semibold"
              >
                <Zap className="w-5 h-5 mr-2" />
                {content.buttonText}
              </Button>

              <p className="text-center text-sm text-gray-500">
                Secure payment processing • Multiple payment methods available
              </p>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center justify-center gap-6 text-sm text-gray-500 pt-4 border-t border-violet-100">
              <div className="flex items-center gap-1">
                <Shield className="w-4 h-4" />
                <span>Secure Payment</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="w-4 h-4" />
                <span>Instant Access</span>
              </div>
              <div className="flex items-center gap-1">
                <Sparkles className="w-4 h-4" />
                <span>Cancel Anytime</span>
              </div>
            </div>

            {/* Trial Info */}
            {subscriptionStatus?.is_trial && subscriptionStatus?.trial_days_remaining > 0 && (
              <div className="text-center text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
                <Clock className="w-4 h-4 inline mr-1" />
                You have {subscriptionStatus.trial_days_remaining} days left in your free trial
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
