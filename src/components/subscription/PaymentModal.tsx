import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  Lock,
  Loader2,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (paymentData: any) => void;
  amount: number; // in cents
  currency: string;
  description: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
}

export default function PaymentModal({
  isOpen,
  onClose,
  onSuccess,
  amount,
  currency,
  description
}: PaymentModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState('card');
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });
  const [mobileData, setMobileData] = useState({
    phone: '',
    provider: 'mpesa'
  });

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      icon: <CreditCard className="w-5 h-5" />,
      description: 'Visa, Mastercard, American Express'
    },
    {
      id: 'mobile',
      name: 'Mobile Money',
      icon: <Smartphone className="w-5 h-5" />,
      description: 'M-Pesa, Airtel Money, MTN Mobile Money'
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      icon: <Building className="w-5 h-5" />,
      description: 'Direct bank transfer'
    }
  ];

  const formatAmount = (cents: number) => {
    return (cents / 100).toFixed(2);
  };

  const handleCardPayment = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate mock payment reference
      const reference = `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      onSuccess({
        reference,
        provider: 'stripe',
        method: 'card',
        amount,
        currency
      });
      
      toast.success('Payment successful!');
    } catch (error) {
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleMobilePayment = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate mobile money payment
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const reference = `mobile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      onSuccess({
        reference,
        provider: 'paystack',
        method: 'mobile_money',
        amount,
        currency
      });
      
      toast.success('Payment successful!');
    } catch (error) {
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBankTransfer = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate bank transfer
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const reference = `bank_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      onSuccess({
        reference,
        provider: 'flutterwave',
        method: 'bank_transfer',
        amount,
        currency
      });
      
      toast.success('Payment successful!');
    } catch (error) {
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const isCardValid = cardData.number.length >= 16 && cardData.expiry.length >= 5 && cardData.cvv.length >= 3 && cardData.name.length >= 2;
  const isMobileValid = mobileData.phone.length >= 10;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Lock className="w-5 h-5 text-green-500" />
            Secure Payment
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Amount Summary */}
          <Card className="bg-gray-50">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{description}</span>
                <span className="font-semibold">${formatAmount(amount)} {currency}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Tabs value={selectedMethod} onValueChange={setSelectedMethod}>
            <TabsList className="grid w-full grid-cols-3">
              {paymentMethods.map((method) => (
                <TabsTrigger key={method.id} value={method.id} className="text-xs">
                  {method.icon}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Card Payment */}
            <TabsContent value="card" className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <CreditCard className="w-4 h-4" />
                    Card Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="card-number" className="text-xs">Card Number</Label>
                    <Input
                      id="card-number"
                      placeholder="1234 5678 9012 3456"
                      value={cardData.number}
                      onChange={(e) => setCardData(prev => ({ ...prev, number: e.target.value }))}
                      maxLength={19}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="expiry" className="text-xs">Expiry</Label>
                      <Input
                        id="expiry"
                        placeholder="MM/YY"
                        value={cardData.expiry}
                        onChange={(e) => setCardData(prev => ({ ...prev, expiry: e.target.value }))}
                        maxLength={5}
                      />
                    </div>
                    <div>
                      <Label htmlFor="cvv" className="text-xs">CVV</Label>
                      <Input
                        id="cvv"
                        placeholder="123"
                        value={cardData.cvv}
                        onChange={(e) => setCardData(prev => ({ ...prev, cvv: e.target.value }))}
                        maxLength={4}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="card-name" className="text-xs">Cardholder Name</Label>
                    <Input
                      id="card-name"
                      placeholder="John Doe"
                      value={cardData.name}
                      onChange={(e) => setCardData(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <Button
                    onClick={handleCardPayment}
                    disabled={!isCardValid || isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Lock className="w-4 h-4 mr-2" />
                        Pay ${formatAmount(amount)}
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Mobile Money */}
            <TabsContent value="mobile" className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Smartphone className="w-4 h-4" />
                    Mobile Money
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="phone" className="text-xs">Phone Number</Label>
                    <Input
                      id="phone"
                      placeholder="+254 700 000 000"
                      value={mobileData.phone}
                      onChange={(e) => setMobileData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <Button
                    onClick={handleMobilePayment}
                    disabled={!isMobileValid || isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Smartphone className="w-4 h-4 mr-2" />
                        Pay ${formatAmount(amount)}
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Bank Transfer */}
            <TabsContent value="bank" className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Building className="w-4 h-4" />
                    Bank Transfer
                  </CardTitle>
                  <CardDescription className="text-xs">
                    You will be redirected to your bank's secure portal
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={handleBankTransfer}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Building className="w-4 h-4 mr-2" />
                        Pay ${formatAmount(amount)}
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Security Notice */}
          <div className="flex items-center gap-2 text-xs text-gray-500 bg-green-50 p-2 rounded">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>Your payment is secured with 256-bit SSL encryption</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
