import React, { useState, useEffect } from 'react';
import { useHasActiveAccess, useSubscriptionStatus } from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
import SubscriptionWall from './SubscriptionWall';
import { Loader2 } from 'lucide-react';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  feature?: string;
  fallback?: React.ReactNode;
  showWallImmediately?: boolean;
}

export default function SubscriptionGuard({
  children,
  feature = "this feature",
  fallback,
  showWallImmediately = false
}: SubscriptionGuardProps) {
  const { data: user, isLoading: authLoading } = useUser();
  const { data: hasAccess, isLoading: accessLoading } = useHasActiveAccess();
  const { data: subscriptionStatus, isLoading: statusLoading } = useSubscriptionStatus();
  const [showWall, setShowWall] = useState(false);

  const isLoading = authLoading || accessLoading || statusLoading;

  useEffect(() => {
    if (!isLoading && user && hasAccess === false) {
      if (showWallImmediately) {
        setShowWall(true);
      }
    }
  }, [isLoading, user, hasAccess, showWallImmediately]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-violet-500" />
        <span className="ml-2 text-gray-600">Checking access...</span>
      </div>
    );
  }

  // User not authenticated
  if (!user) {
    return fallback || (
      <div className="text-center p-8">
        <p className="text-gray-600">Please log in to access this feature.</p>
      </div>
    );
  }

  // User has active access
  if (hasAccess) {
    return <>{children}</>;
  }

  // User doesn't have access - show fallback or trigger wall
  const handleAccessDenied = () => {
    setShowWall(true);
  };

  return (
    <>
      {fallback || (
        <div className="text-center p-8 bg-gradient-to-br from-violet-50 to-purple-50 rounded-lg border border-violet-200">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Upgrade Required
            </h3>
            <p className="text-gray-600 mb-4">
              You need an active subscription to access {feature}.
            </p>
            <button
              onClick={handleAccessDenied}
              className="bg-gradient-to-r from-violet-500 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-violet-600 hover:to-purple-700 transition-colors"
            >
              Upgrade Now
            </button>
          </div>
        </div>
      )}

      <SubscriptionWall
        isOpen={showWall}
        onClose={() => setShowWall(false)}
        feature={feature}
      />
    </>
  );
}

// Higher-order component for protecting routes
export function withSubscriptionGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    feature?: string;
    showWallImmediately?: boolean;
  }
) {
  return function ProtectedComponent(props: P) {
    return (
      <SubscriptionGuard 
        feature={options?.feature}
        showWallImmediately={options?.showWallImmediately}
      >
        <Component {...props} />
      </SubscriptionGuard>
    );
  };
}

// Hook for checking access and showing wall
export function useSubscriptionAccess(feature?: string) {
  const { data: hasAccess, isLoading } = useHasActiveAccess();
  const [showWall, setShowWall] = useState(false);

  const checkAccess = () => {
    if (!hasAccess && !isLoading) {
      setShowWall(true);
      return false;
    }
    return hasAccess;
  };

  const SubscriptionWallComponent = () => (
    <SubscriptionWall
      isOpen={showWall}
      onClose={() => setShowWall(false)}
      feature={feature}
    />
  );

  return {
    hasAccess,
    isLoading,
    checkAccess,
    showWall,
    setShowWall,
    SubscriptionWall: SubscriptionWallComponent
  };
}
