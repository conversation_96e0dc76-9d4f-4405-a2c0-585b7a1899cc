// IndexedDB utility for offline data storage
const DB_NAME = 'StudyFamOfflineDB';
const DB_VERSION = 3; // Incremented again to force fresh database

// Object store names
export const STORES = {
  UNITS: 'units',
  TOPICS: 'topics',
  NOTES: 'notes',
  TIMETABLE: 'timetable',
  TIMETABLE_SESSIONS: 'timetable_sessions',
  MESSAGES: 'messages',
  GROUPS: 'groups',
  GROUP_DISCUSSIONS: 'group_discussions',
  PAST_PAPERS: 'past_papers',
  OFFLINE_ACTIONS: 'offline_actions',
  USER_DATA: 'user_data',
  CACHED_DATA: 'cached_data'
} as const;

// Data types for offline storage
export interface OfflineUnit {
  id: string;
  name: string;
  description?: string;
  color: string;
  topic_count: number;
  note_count: number;
  created_at: string;
  updated_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineTopic {
  id: string;
  name: string;
  description?: string;
  unit_id: string;
  note_count: number;
  created_at: string;
  updated_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineNote {
  id: string;
  title: string;
  content: string;
  unit_id?: string;
  topic_id?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineTimetableEntry {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  day_of_week: number;
  color?: string;
  created_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineMessage {
  id: string;
  content: string;
  sender_id: string;
  receiver_id?: string;
  group_id?: string;
  created_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineTimetableSession {
  id: string;
  timetable_entry_id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  date: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
  created_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineGroup {
  id: string;
  name: string;
  description?: string;
  subject?: string;
  image_url?: string;
  is_private: boolean;
  created_by?: string;
  member_count: number;
  is_member: boolean;
  created_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineDiscussion {
  id: string;
  group_id: string;
  title: string;
  content: string;
  author_id: string;
  author_name: string;
  replies_count: number;
  created_at: string;
  updated_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflinePastPaper {
  id: string;
  title: string;
  subject: string;
  year: number;
  exam_board?: string;
  file_url?: string;
  file_content?: string; // For offline access
  description?: string;
  created_at: string;
  synced: boolean;
  offline_id?: string;
}

export interface OfflineAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  store: string;
  data: any;
  timestamp: string;
  synced: boolean;
}

class OfflineStorage {
  private db: IDBDatabase | null = null;

  // Initialize the database
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        if (!db.objectStoreNames.contains(STORES.UNITS)) {
          const unitsStore = db.createObjectStore(STORES.UNITS, { keyPath: 'id' });
          unitsStore.createIndex('synced', 'synced', { unique: false });
          unitsStore.createIndex('created_at', 'created_at', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.TOPICS)) {
          const topicsStore = db.createObjectStore(STORES.TOPICS, { keyPath: 'id' });
          topicsStore.createIndex('synced', 'synced', { unique: false });
          topicsStore.createIndex('unit_id', 'unit_id', { unique: false });
          topicsStore.createIndex('created_at', 'created_at', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.NOTES)) {
          const notesStore = db.createObjectStore(STORES.NOTES, { keyPath: 'id' });
          notesStore.createIndex('synced', 'synced', { unique: false });
          notesStore.createIndex('unit_id', 'unit_id', { unique: false });
          notesStore.createIndex('topic_id', 'topic_id', { unique: false });
          notesStore.createIndex('created_at', 'created_at', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.TIMETABLE)) {
          const timetableStore = db.createObjectStore(STORES.TIMETABLE, { keyPath: 'id' });
          timetableStore.createIndex('synced', 'synced', { unique: false });
          timetableStore.createIndex('day_of_week', 'day_of_week', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.MESSAGES)) {
          const messagesStore = db.createObjectStore(STORES.MESSAGES, { keyPath: 'id' });
          messagesStore.createIndex('synced', 'synced', { unique: false });
          messagesStore.createIndex('group_id', 'group_id', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.GROUPS)) {
          const groupsStore = db.createObjectStore(STORES.GROUPS, { keyPath: 'id' });
          groupsStore.createIndex('synced', 'synced', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.TIMETABLE_SESSIONS)) {
          const sessionsStore = db.createObjectStore(STORES.TIMETABLE_SESSIONS, { keyPath: 'id' });
          sessionsStore.createIndex('synced', 'synced', { unique: false });
          sessionsStore.createIndex('date', 'date', { unique: false });
          sessionsStore.createIndex('status', 'status', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.GROUP_DISCUSSIONS)) {
          const discussionsStore = db.createObjectStore(STORES.GROUP_DISCUSSIONS, { keyPath: 'id' });
          discussionsStore.createIndex('synced', 'synced', { unique: false });
          discussionsStore.createIndex('group_id', 'group_id', { unique: false });
          discussionsStore.createIndex('created_at', 'created_at', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.PAST_PAPERS)) {
          const papersStore = db.createObjectStore(STORES.PAST_PAPERS, { keyPath: 'id' });
          papersStore.createIndex('synced', 'synced', { unique: false });
          papersStore.createIndex('subject', 'subject', { unique: false });
          papersStore.createIndex('year', 'year', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.OFFLINE_ACTIONS)) {
          const actionsStore = db.createObjectStore(STORES.OFFLINE_ACTIONS, { keyPath: 'id' });
          actionsStore.createIndex('synced', 'synced', { unique: false });
          actionsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.USER_DATA)) {
          db.createObjectStore(STORES.USER_DATA, { keyPath: 'key' });
        }

        if (!db.objectStoreNames.contains(STORES.CACHED_DATA)) {
          const cachedStore = db.createObjectStore(STORES.CACHED_DATA, { keyPath: 'key' });
          cachedStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  // Generic method to add data to a store
  async add(storeName: string, data: any): Promise<void> {
    if (!this.db) {
      console.error('Database not initialized when trying to add to', storeName);
      throw new Error('Database not initialized');
    }

    console.log('Adding data to store:', storeName, data);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => {
        console.log('Successfully added data to', storeName);
        resolve();
      };
      request.onerror = () => {
        console.error('Error adding data to', storeName, request.error);
        reject(request.error);
      };
    });
  }

  // Generic method to update data in a store
  async put(storeName: string, data: any): Promise<void> {
    if (!this.db) {
      console.error('Database not initialized when trying to put to', storeName);
      throw new Error('Database not initialized');
    }

    // Check if the store exists
    if (!this.db.objectStoreNames.contains(storeName)) {
      console.error('Store does not exist:', storeName);
      throw new Error(`Store '${storeName}' does not exist in database`);
    }

    console.log('Putting data to store:', storeName, data);

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.put(data);

        request.onsuccess = () => {
          console.log('Successfully put data to', storeName);
          resolve();
        };

        request.onerror = () => {
          console.error('Error putting data to', storeName, request.error);
          reject(new Error(`Failed to save to ${storeName}: ${request.error?.message || 'Unknown error'}`));
        };

        transaction.onerror = () => {
          console.error('Transaction error for', storeName, transaction.error);
          reject(new Error(`Transaction failed for ${storeName}: ${transaction.error?.message || 'Unknown error'}`));
        };
      } catch (error) {
        console.error('Exception in put operation:', error);
        reject(error);
      }
    });
  }

  // Generic method to get data from a store
  async get(storeName: string, id: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // Generic method to get all data from a store
  async getAll(storeName: string): Promise<any[]> {
    if (!this.db) {
      console.error('Database not initialized when trying to get from', storeName);
      throw new Error('Database not initialized');
    }

    console.log('Getting all data from store:', storeName);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        console.log('Successfully got data from', storeName, 'count:', request.result.length);
        resolve(request.result);
      };
      request.onerror = () => {
        console.error('Error getting data from', storeName, request.error);
        reject(request.error);
      };
    });
  }

  // Get unsynced data from a store
  async getUnsynced(storeName: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        // Check if the store has a 'synced' index
        if (!store.indexNames.contains('synced')) {
          console.log('Store', storeName, 'does not have synced index, returning empty array');
          resolve([]);
          return;
        }

        // Get all records and filter manually to avoid IDBKeyRange issues
        const request = store.getAll();

        request.onsuccess = () => {
          const allRecords = request.result;
          const unsyncedRecords = allRecords.filter(record => record.synced === false);
          console.log('Found', unsyncedRecords.length, 'unsynced records in', storeName);
          resolve(unsyncedRecords);
        };
        request.onerror = () => {
          console.error('Error getting unsynced data from', storeName, request.error);
          reject(request.error);
        };
      } catch (error) {
        console.error('Exception in getUnsynced:', error);
        resolve([]); // Return empty array instead of rejecting
      }
    });
  }

  // Delete data from a store
  async delete(storeName: string, id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Clear all data from a store
  async clear(storeName: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Mark data as synced
  async markAsSynced(storeName: string, id: string): Promise<void> {
    const data = await this.get(storeName, id);
    if (data) {
      data.synced = true;
      await this.put(storeName, data);
    }
  }

  // Add offline action for later sync
  async addOfflineAction(action: Omit<OfflineAction, 'id'>): Promise<void> {
    const offlineAction: OfflineAction = {
      ...action,
      id: `offline_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    };

    await this.add(STORES.OFFLINE_ACTIONS, offlineAction);
  }

  // Get storage usage info
  async getStorageInfo(): Promise<{ used: number; quota: number }> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0
      };
    }
    return { used: 0, quota: 0 };
  }

  // Check if database is ready and has all required stores
  isReady(): boolean {
    if (!this.db) {
      console.log('Database not initialized');
      return false;
    }

    const requiredStores = Object.values(STORES);
    const missingStores = requiredStores.filter(store => !this.db!.objectStoreNames.contains(store));

    if (missingStores.length > 0) {
      console.log('Missing stores:', missingStores);
      return false;
    }

    console.log('Database is ready with all stores');
    return true;
  }

  // Wait for database to be ready
  async waitForReady(timeout = 5000): Promise<void> {
    const startTime = Date.now();

    while (!this.isReady() && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!this.isReady()) {
      throw new Error('Database not ready within timeout period');
    }
  }

  // Reset database completely
  async reset(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }

    return new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

      deleteRequest.onsuccess = () => {
        console.log('Database deleted successfully');
        // Reinitialize
        this.init().then(resolve).catch(reject);
      };

      deleteRequest.onerror = () => {
        console.error('Error deleting database');
        reject(new Error('Failed to delete database'));
      };
    });
  }
}

// Create singleton instance
export const offlineStorage = new OfflineStorage();

// Initialize on module load with better error handling
offlineStorage.init().then(() => {
  console.log('OfflineStorage initialized successfully');
  // Add some sample data for testing if none exists
  setTimeout(() => {
    initializeSampleData();
  }, 100); // Small delay to ensure DB is fully ready
}).catch((error) => {
  console.error('Failed to initialize OfflineStorage:', error);
});

// Add sample data for testing offline functionality
async function initializeSampleData() {
  try {
    // Check if we already have data
    const [existingUnits, existingTopics, existingNotes, existingGroups] = await Promise.all([
      offlineStorage.getAll(STORES.UNITS),
      offlineStorage.getAll(STORES.TOPICS),
      offlineStorage.getAll(STORES.NOTES),
      offlineStorage.getAll(STORES.GROUPS)
    ]);

    // Clear any existing sample past papers data
    await offlineStorage.clear(STORES.PAST_PAPERS);

    // Add sample units if none exist
    if (existingUnits.length === 0) {
      const sampleUnits = [
        {
          id: 'sample_unit_1',
          name: 'Biology',
          description: 'Study of living organisms',
          color: '#10B981',
          topic_count: 2,
          note_count: 3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_unit_1'
        },
        {
          id: 'sample_unit_2',
          name: 'Mathematics',
          description: 'Mathematical concepts and problem solving',
          color: '#3B82F6',
          topic_count: 1,
          note_count: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_unit_2'
        },
        {
          id: 'sample_unit_3',
          name: 'Physics',
          description: 'Physical sciences and natural phenomena',
          color: '#8B5CF6',
          topic_count: 1,
          note_count: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_unit_3'
        }
      ];

      for (const unit of sampleUnits) {
        await offlineStorage.put(STORES.UNITS, unit);
      }
      console.log('Added sample units');
    }

    // Add sample topics if none exist
    if (existingTopics.length === 0) {
      const sampleTopics = [
        {
          id: 'sample_topic_1',
          name: 'Cell Biology',
          description: 'Structure and function of cells',
          unit_id: 'sample_unit_1',
          note_count: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_topic_1'
        },
        {
          id: 'sample_topic_2',
          name: 'Genetics',
          description: 'Heredity and genetic variation',
          unit_id: 'sample_unit_1',
          note_count: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_topic_2'
        },
        {
          id: 'sample_topic_3',
          name: 'Algebra',
          description: 'Mathematical expressions and equations',
          unit_id: 'sample_unit_2',
          note_count: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_topic_3'
        },
        {
          id: 'sample_topic_4',
          name: 'Mechanics',
          description: 'Motion and forces',
          unit_id: 'sample_unit_3',
          note_count: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_topic_4'
        }
      ];

      for (const topic of sampleTopics) {
        await offlineStorage.put(STORES.TOPICS, topic);
      }
      console.log('Added sample topics');
    }

    // Add sample notes if none exist
    if (existingNotes.length === 0) {
      const sampleNotes = [
        {
          id: 'sample_note_1',
          title: 'Cell Structure and Function',
          content: '# Cell Structure and Function\n\nCells are the basic units of life. All living organisms are composed of one or more cells.\n\n## Key Components\n- Cell membrane\n- Nucleus\n- Cytoplasm\n- Organelles\n\n## Types of Cells\n- Prokaryotic cells\n- Eukaryotic cells',
          unit_id: 'sample_unit_1',
          topic_id: 'sample_topic_1',
          tags: ['biology', 'cells'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false,
          offline_id: 'sample_note_1'
        },
        {
          id: 'sample_note_2',
          title: 'Cell Division Process',
          content: '# Cell Division\n\nCell division is the process by which cells reproduce to create new cells.\n\n## Types of Cell Division\n- Mitosis: Creates identical diploid cells\n- Meiosis: Creates genetically diverse gametes\n\n## Phases of Mitosis\n1. Prophase\n2. Metaphase\n3. Anaphase\n4. Telophase',
          unit_id: 'sample_unit_1',
          topic_id: 'sample_topic_1',
          tags: ['biology', 'cells', 'division'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_note_3',
          title: 'DNA and Heredity',
          content: '# DNA and Heredity\n\nDNA (Deoxyribonucleic acid) carries genetic information in living organisms.\n\n## DNA Structure\n- Double helix structure\n- Base pairs: A-T, G-C\n- Sugar-phosphate backbone\n\n## Heredity\n- Genes are units of heredity\n- Alleles are different versions of genes\n- Dominant and recessive traits',
          unit_id: 'sample_unit_1',
          topic_id: 'sample_topic_2',
          tags: ['biology', 'genetics', 'DNA'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_note_4',
          title: 'Linear Equations',
          content: '# Linear Equations\n\nLinear equations are algebraic equations where each term is either a constant or the product of a constant and a single variable.\n\n## Standard Form\nax + b = 0\n\n## Solving Linear Equations\n1. Isolate the variable\n2. Perform inverse operations\n3. Check your solution\n\n## Examples\n- 2x + 5 = 11\n- 3x - 7 = 14',
          unit_id: 'sample_unit_2',
          topic_id: 'sample_topic_3',
          tags: ['mathematics', 'algebra', 'equations'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_note_5',
          title: 'Quadratic Functions',
          content: '# Quadratic Functions\n\nQuadratic functions are polynomial functions of degree 2.\n\n## Standard Form\nf(x) = ax² + bx + c\n\n## Key Features\n- Parabolic graph\n- Vertex form: f(x) = a(x-h)² + k\n- Axis of symmetry\n- Discriminant: b² - 4ac\n\n## Solving Quadratic Equations\n- Factoring\n- Quadratic formula\n- Completing the square',
          unit_id: 'sample_unit_2',
          topic_id: 'sample_topic_3',
          tags: ['mathematics', 'algebra', 'quadratic'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_note_6',
          title: 'Newton\'s Laws of Motion',
          content: '# Newton\'s Laws of Motion\n\nThree fundamental laws that describe the relationship between forces and motion.\n\n## First Law (Inertia)\nAn object at rest stays at rest, and an object in motion stays in motion, unless acted upon by an external force.\n\n## Second Law (F = ma)\nThe acceleration of an object is directly proportional to the net force acting on it and inversely proportional to its mass.\n\n## Third Law (Action-Reaction)\nFor every action, there is an equal and opposite reaction.',
          unit_id: 'sample_unit_3',
          topic_id: 'sample_topic_4',
          tags: ['physics', 'mechanics', 'newton'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          synced: false
        }
      ];

      for (const note of sampleNotes) {
        await offlineStorage.put(STORES.NOTES, note);
      }
      console.log('Added sample notes');
    }

    // Add sample groups if none exist
    if (existingGroups.length === 0) {
      const sampleGroups = [
        {
          id: 'sample_group_1',
          name: 'Biology Study Group',
          description: 'A group for students studying biology together',
          subject: 'Biology',
          is_private: false,
          member_count: 15,
          is_member: true,
          created_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_group_2',
          name: 'Mathematics Help',
          description: 'Get help with mathematics problems and concepts',
          subject: 'Mathematics',
          is_private: false,
          member_count: 23,
          is_member: true,
          created_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_group_3',
          name: 'Physics Discussion',
          description: 'Discuss physics concepts and solve problems together',
          subject: 'Physics',
          is_private: false,
          member_count: 12,
          is_member: false,
          created_at: new Date().toISOString(),
          synced: false
        },
        {
          id: 'sample_group_4',
          name: 'Information Technology',
          description: 'Learn programming, web development, and computer science concepts together',
          subject: 'Computer Science',
          image_url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop&crop=center',
          is_private: false,
          member_count: 28,
          is_member: true,
          created_at: new Date().toISOString(),
          synced: false
        }
      ];

      for (const group of sampleGroups) {
        await offlineStorage.put(STORES.GROUPS, group);
      }
      console.log('Added sample groups');

      // Add sample discussions for the groups
      const sampleDiscussions = [
        {
          id: 'sample_discussion_1',
          group_id: 'sample_group_1',
          title: 'Cell Division Process',
          content: 'Can someone explain the difference between mitosis and meiosis? I\'m having trouble understanding the key differences.',
          author_id: 'user_1',
          author_name: 'Sarah Johnson',
          replies_count: 3,
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          synced: false
        },
        {
          id: 'sample_discussion_2',
          group_id: 'sample_group_2',
          title: 'Quadratic Equations Help',
          content: 'I\'m struggling with solving quadratic equations using the quadratic formula. Any tips or tricks?',
          author_id: 'user_2',
          author_name: 'Mike Chen',
          replies_count: 5,
          created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
          updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          synced: false
        },
        {
          id: 'sample_discussion_3',
          group_id: 'sample_group_4',
          title: 'JavaScript vs Python for Beginners',
          content: 'I\'m new to programming and wondering which language to start with. What are the pros and cons of JavaScript vs Python for a complete beginner?',
          author_id: 'user_4',
          author_name: 'Alex Rodriguez',
          replies_count: 8,
          created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          synced: false
        },
        {
          id: 'sample_discussion_4',
          group_id: 'sample_group_4',
          title: 'Web Development Project Ideas',
          content: 'Looking for some beginner-friendly web development project ideas to build my portfolio. Any suggestions for projects that showcase different skills?',
          author_id: 'user_5',
          author_name: 'Jordan Kim',
          replies_count: 12,
          created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
          updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          synced: false
        },
        {
          id: 'sample_discussion_5',
          group_id: 'sample_group_4',
          title: 'React vs Vue.js - Which to Learn First?',
          content: 'I\'ve learned basic JavaScript and want to move to a framework. Should I start with React or Vue.js? What are the learning curves like?',
          author_id: 'user_6',
          author_name: 'Taylor Swift',
          replies_count: 6,
          created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
          updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          synced: false
        }
      ];

      for (const discussion of sampleDiscussions) {
        await offlineStorage.put(STORES.GROUP_DISCUSSIONS, discussion);
      }
      console.log('Added sample discussions');
    }

    // No longer adding sample papers - use real database data only


  } catch (error) {
    console.error('Error initializing sample data:', error);
  }
}

// Utility functions for specific data types
export const OfflineUnits = {
  async save(unit: Omit<OfflineUnit, 'id' | 'synced'>): Promise<string> {
    const id = `unit_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineUnit: OfflineUnit = {
      ...unit,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.UNITS, offlineUnit);

    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.UNITS,
      data: offlineUnit,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineUnit[]> {
    return offlineStorage.getAll(STORES.UNITS);
  },

  async update(id: string, updates: Partial<OfflineUnit>): Promise<void> {
    const existing = await offlineStorage.get(STORES.UNITS, id);
    if (existing) {
      const updated = { ...existing, ...updates, updated_at: new Date().toISOString() };
      await offlineStorage.put(STORES.UNITS, updated);

      await offlineStorage.addOfflineAction({
        type: 'UPDATE',
        store: STORES.UNITS,
        data: updated,
        timestamp: new Date().toISOString(),
        synced: false
      });
    }
  }
};

export const OfflineTopics = {
  async save(topic: Omit<OfflineTopic, 'id' | 'synced'>): Promise<string> {
    const id = `topic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineTopic: OfflineTopic = {
      ...topic,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.TOPICS, offlineTopic);

    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.TOPICS,
      data: offlineTopic,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineTopic[]> {
    return offlineStorage.getAll(STORES.TOPICS);
  },

  async getByUnit(unitId: string): Promise<OfflineTopic[]> {
    const all = await this.getAll();
    return all.filter((topic: OfflineTopic) => topic.unit_id === unitId);
  },

  async update(id: string, updates: Partial<OfflineTopic>): Promise<void> {
    const existing = await offlineStorage.get(STORES.TOPICS, id);
    if (existing) {
      const updated = { ...existing, ...updates, updated_at: new Date().toISOString() };
      await offlineStorage.put(STORES.TOPICS, updated);

      await offlineStorage.addOfflineAction({
        type: 'UPDATE',
        store: STORES.TOPICS,
        data: updated,
        timestamp: new Date().toISOString(),
        synced: false
      });
    }
  }
};

export const OfflineNotes = {
  async save(note: Omit<OfflineNote, 'id' | 'synced'>): Promise<string> {
    const id = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineNote: OfflineNote = {
      ...note,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.NOTES, offlineNote);

    // Add to offline actions for sync
    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.NOTES,
      data: offlineNote,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineNote[]> {
    return offlineStorage.getAll(STORES.NOTES);
  },

  async getByUnit(unitId: string): Promise<OfflineNote[]> {
    const all = await this.getAll();
    return all.filter((note: OfflineNote) => note.unit_id === unitId);
  },

  async getByTopic(topicId: string): Promise<OfflineNote[]> {
    const all = await this.getAll();
    return all.filter((note: OfflineNote) => note.topic_id === topicId);
  },

  async update(id: string, updates: Partial<OfflineNote>): Promise<void> {
    const existing = await offlineStorage.get(STORES.NOTES, id);
    if (existing) {
      const updated = { ...existing, ...updates, updated_at: new Date().toISOString() };
      await offlineStorage.put(STORES.NOTES, updated);

      await offlineStorage.addOfflineAction({
        type: 'UPDATE',
        store: STORES.NOTES,
        data: updated,
        timestamp: new Date().toISOString(),
        synced: false
      });
    }
  },

  async delete(id: string): Promise<void> {
    await offlineStorage.delete(STORES.NOTES, id);

    await offlineStorage.addOfflineAction({
      type: 'DELETE',
      store: STORES.NOTES,
      data: { id },
      timestamp: new Date().toISOString(),
      synced: false
    });
  }
};

export const OfflineTimetable = {
  async save(entry: Omit<OfflineTimetableEntry, 'id' | 'synced'>): Promise<string> {
    const id = `timetable_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineEntry: OfflineTimetableEntry = {
      ...entry,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.TIMETABLE, offlineEntry);

    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.TIMETABLE,
      data: offlineEntry,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineTimetableEntry[]> {
    return offlineStorage.getAll(STORES.TIMETABLE);
  },

  async getByDay(dayOfWeek: number): Promise<OfflineTimetableEntry[]> {
    const all = await this.getAll();
    return all.filter((entry: OfflineTimetableEntry) => entry.day_of_week === dayOfWeek);
  }
};

export const OfflineMessages = {
  async save(message: Omit<OfflineMessage, 'id' | 'synced'>): Promise<string> {
    const id = `message_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineMessage: OfflineMessage = {
      ...message,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.MESSAGES, offlineMessage);

    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.MESSAGES,
      data: offlineMessage,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineMessage[]> {
    return offlineStorage.getAll(STORES.MESSAGES);
  },

  async getByGroup(groupId: string): Promise<OfflineMessage[]> {
    const all = await this.getAll();
    return all.filter((message: OfflineMessage) => message.group_id === groupId);
  }
};

// Timetable Sessions utilities
export const OfflineTimetableSessions = {
  async save(session: Omit<OfflineTimetableSession, 'id' | 'synced'>): Promise<string> {
    const id = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineSession: OfflineTimetableSession = {
      ...session,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.TIMETABLE_SESSIONS, offlineSession);

    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.TIMETABLE_SESSIONS,
      data: offlineSession,
      timestamp: new Date().toISOString(),
      synced: false
    });

    return id;
  },

  async getAll(): Promise<OfflineTimetableSession[]> {
    return offlineStorage.getAll(STORES.TIMETABLE_SESSIONS);
  },

  async getByDate(date: string): Promise<OfflineTimetableSession[]> {
    const all = await this.getAll();
    return all.filter((session: OfflineTimetableSession) => session.date === date);
  },

  async updateStatus(id: string, status: OfflineTimetableSession['status'], notes?: string): Promise<void> {
    const existing = await offlineStorage.get(STORES.TIMETABLE_SESSIONS, id);
    if (existing) {
      const updated = {
        ...existing,
        status,
        notes: notes || existing.notes,
        synced: false
      };
      await offlineStorage.put(STORES.TIMETABLE_SESSIONS, updated);

      await offlineStorage.addOfflineAction({
        type: 'UPDATE',
        store: STORES.TIMETABLE_SESSIONS,
        data: updated,
        timestamp: new Date().toISOString(),
        synced: false
      });
    }
  }
};

// Groups utilities
export const OfflineGroups = {
  async save(group: Omit<OfflineGroup, 'id' | 'synced'>): Promise<string> {
    const id = `group_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineGroup: OfflineGroup = {
      ...group,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.GROUPS, offlineGroup);
    return id;
  },

  async getAll(): Promise<OfflineGroup[]> {
    return offlineStorage.getAll(STORES.GROUPS);
  },

  async getJoinedGroups(): Promise<OfflineGroup[]> {
    const all = await this.getAll();
    return all.filter((group: OfflineGroup) => group.is_member);
  }
};

// Group Discussions utilities
export const OfflineDiscussions = {
  async save(discussion: Omit<OfflineDiscussion, 'id' | 'synced'>): Promise<string> {
    console.log('OfflineDiscussions.save called with:', discussion);

    const id = `discussion_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlineDiscussion: OfflineDiscussion = {
      ...discussion,
      id,
      synced: false,
      offline_id: id
    };

    console.log('Saving discussion to store:', STORES.GROUP_DISCUSSIONS, offlineDiscussion);

    // Simple direct save without complex error handling
    await offlineStorage.put(STORES.GROUP_DISCUSSIONS, offlineDiscussion);
    console.log('Discussion saved to IndexedDB');

    // Add offline action for sync
    await offlineStorage.addOfflineAction({
      type: 'CREATE',
      store: STORES.GROUP_DISCUSSIONS,
      data: offlineDiscussion,
      timestamp: new Date().toISOString(),
      synced: false
    });
    console.log('Offline action added');

    console.log('Discussion saved successfully with ID:', id);
    return id;
  },

  async getAll(): Promise<OfflineDiscussion[]> {
    console.log('OfflineDiscussions.getAll called');
    const discussions = await offlineStorage.getAll(STORES.GROUP_DISCUSSIONS);
    console.log('Retrieved discussions:', discussions.length, discussions);
    return discussions;
  },

  async getByGroup(groupId: string): Promise<OfflineDiscussion[]> {
    console.log('OfflineDiscussions.getByGroup called for group:', groupId);
    const all = await this.getAll();
    const filtered = all.filter((discussion: OfflineDiscussion) => discussion.group_id === groupId);
    console.log('Filtered discussions for group:', filtered.length, filtered);
    return filtered;
  }
};

// Past Papers utilities
export const OfflinePastPapers = {
  async save(paper: Omit<OfflinePastPaper, 'id' | 'synced'>): Promise<string> {
    const id = `paper_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const offlinePaper: OfflinePastPaper = {
      ...paper,
      id,
      synced: false,
      offline_id: id
    };

    await offlineStorage.put(STORES.PAST_PAPERS, offlinePaper);
    return id;
  },

  async getAll(): Promise<OfflinePastPaper[]> {
    return offlineStorage.getAll(STORES.PAST_PAPERS);
  },

  async getBySubject(subject: string): Promise<OfflinePastPaper[]> {
    const all = await this.getAll();
    return all.filter((paper: OfflinePastPaper) =>
      paper.subject.toLowerCase().includes(subject.toLowerCase())
    );
  },

  async getByYear(year: number): Promise<OfflinePastPaper[]> {
    const all = await this.getAll();
    return all.filter((paper: OfflinePastPaper) => paper.year === year);
  }
};
