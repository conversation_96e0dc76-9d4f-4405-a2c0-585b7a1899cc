// Advanced PDF text extraction using multiple methods for maximum reliability
import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker for version 3.11.174
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

export interface TextExtractionResult {
  success: boolean;
  text?: string;
  error?: string;
  metadata?: {
    fileType: string;
    fileName: string;
    fileSize: number;
    pageCount?: number;
    wordCount?: number;
    extractionMethod?: string;
  };
}

/**
 * Advanced text processing for PDF.js text content
 */
function processAdvancedTextContent(textContent: any): string {
  if (!textContent || !textContent.items) {
    return '';
  }

  const items = textContent.items;
  let processedText = '';
  let lastY = -1;
  let lastX = -1;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const text = item.str || '';
    
    if (!text.trim()) continue;

    // Get position information
    const transform = item.transform;
    const x = transform ? transform[4] : 0;
    const y = transform ? transform[5] : 0;

    // Add line breaks based on Y position changes (new lines)
    if (lastY !== -1 && Math.abs(y - lastY) > 5) {
      processedText += '\n';
    }
    // Add spaces based on X position changes (word spacing)
    else if (lastX !== -1 && x - lastX > 10) {
      processedText += ' ';
    }

    processedText += text;
    lastY = y;
    lastX = x + (item.width || 0);
  }

  return processedText;
}

/**
 * Advanced PDF.js extraction with proper text positioning and formatting
 */
async function extractWithAdvancedPDFJS(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  const loadingTask = pdfjsLib.getDocument({
    data: arrayBuffer,
    cMapUrl: `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/`,
    cMapPacked: true,
    standardFontDataUrl: `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/standard_fonts/`,
  });

  const pdf = await loadingTask.promise;
  const pageCount = pdf.numPages;
  console.log(`📄 Advanced extraction - PDF loaded: ${pageCount} pages`);

  let extractedText = '';
  const maxPages = Math.min(pageCount, 150);

  for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      // Advanced text processing with positioning
      const pageText = processAdvancedTextContent(textContent);
      
      if (pageText.trim()) {
        extractedText += `${pageText}\n\n`;
      }
      
      // Progress logging
      if (pageNum % 10 === 0 || pageNum === maxPages) {
        console.log(`📖 Advanced: Processed ${pageNum}/${maxPages} pages...`);
      }
    } catch (pageError) {
      console.warn(`⚠️ Advanced: Error on page ${pageNum}:`, pageError);
    }
  }

  // Clean and process the extracted text
  let cleanText = extractedText
    .replace(/\n{3,}/g, '\n\n') // Limit consecutive line breaks
    .replace(/[ \t]+/g, ' ') // Normalize spaces and tabs
    .trim();

  if (pageCount > maxPages) {
    cleanText += `\n\n[Note: This PDF has ${pageCount} pages. Only the first ${maxPages} pages were processed.]`;
  }

  if (cleanText.length > 100) {
    const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
    console.log(`✅ Advanced extraction successful: ${wordCount} words from ${pageCount} pages`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        pageCount,
        wordCount,
        extractionMethod: 'Advanced PDF.js'
      }
    };
  }

  throw new Error('Advanced extraction: Insufficient text extracted');
}

/**
 * Basic PDF.js extraction (fallback method)
 */
async function extractWithBasicPDFJS(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
  const pdf = await loadingTask.promise;
  const pageCount = pdf.numPages;
  
  console.log(`📄 Basic extraction - PDF loaded: ${pageCount} pages`);

  let extractedText = '';
  const maxPages = Math.min(pageCount, 100);

  for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      
      // Simple text extraction
      const pageText = textContent.items
        .map((item: any) => item.str || '')
        .filter(str => str.trim())
        .join(' ');
      
      if (pageText.trim()) {
        extractedText += pageText + '\n\n';
      }
      
      if (pageNum % 20 === 0) {
        console.log(`📖 Basic: Processed ${pageNum}/${maxPages} pages...`);
      }
    } catch (pageError) {
      console.warn(`⚠️ Basic: Error on page ${pageNum}:`, pageError);
    }
  }

  let cleanText = extractedText
    .replace(/\s+/g, ' ')
    .trim();

  if (cleanText.length > 50) {
    const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
    console.log(`✅ Basic extraction successful: ${wordCount} words`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        pageCount,
        wordCount,
        extractionMethod: 'Basic PDF.js'
      }
    };
  }

  throw new Error('Basic extraction: Insufficient text extracted');
}

/**
 * Binary parsing extraction (last resort fallback)
 */
async function extractWithBinaryParsing(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  console.log('📄 Binary parsing - attempting text extraction...');
  
  const uint8Array = new Uint8Array(arrayBuffer);
  let extractedText = '';
  let currentWord = '';

  // Look for readable text patterns in the binary data
  for (let i = 0; i < uint8Array.length; i++) {
    const byte = uint8Array[i];
    
    // Check for readable ASCII characters
    if ((byte >= 32 && byte <= 126) || byte === 10 || byte === 13) {
      const char = String.fromCharCode(byte);
      
      if (char.match(/[a-zA-Z0-9.,!?;:'"()\-\s]/)) {
        currentWord += char;
      } else {
        if (currentWord.trim().length > 2) {
          extractedText += currentWord + ' ';
        }
        currentWord = '';
      }
    } else {
      if (currentWord.trim().length > 2) {
        extractedText += currentWord + ' ';
      }
      currentWord = '';
    }
  }

  // Add the last word
  if (currentWord.trim().length > 2) {
    extractedText += currentWord;
  }

  // Clean and filter the text
  const words = extractedText.split(/\s+/);
  const filteredWords = words.filter(word => {
    return !word.match(/^(PDF|obj|endobj|stream|endstream|xref|trailer|BT|ET|Tf|Tj|TJ|Type|Page|Parent|Resources|MediaBox|Contents|Length|Filter|FlateDecode)$/i) &&
           word.length > 1 &&
           !word.match(/^[0-9.]+$/) &&
           !word.match(/^[\/\\<>{}%\[\]]+$/);
  });

  const cleanText = filteredWords.join(' ').trim();

  if (cleanText.length > 50) {
    const wordCount = filteredWords.length;
    console.log(`✅ Binary parsing successful: ${wordCount} words`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        wordCount,
        extractionMethod: 'Binary Parsing'
      }
    };
  }

  throw new Error('Binary parsing: No readable text found');
}

/**
 * Main PDF text extraction function with multiple fallback methods
 */
export async function extractTextFromPDF(file: File): Promise<TextExtractionResult> {
  console.log('🚀 Starting advanced PDF text extraction for:', file.name);

  try {
    const arrayBuffer = await file.arrayBuffer();
    
    // Try multiple extraction methods in order of preference
    const methods = [
      () => extractWithAdvancedPDFJS(arrayBuffer, file.name),
      () => extractWithBasicPDFJS(arrayBuffer, file.name),
      () => extractWithBinaryParsing(arrayBuffer, file.name)
    ];

    for (let i = 0; i < methods.length; i++) {
      try {
        console.log(`🔄 Trying extraction method ${i + 1}...`);
        const result = await methods[i]();
        
        if (result.success && result.text && result.text.length > 100) {
          console.log(`✅ Method ${i + 1} successful: ${result.text.length} characters extracted`);
          return result;
        } else {
          console.log(`⚠️ Method ${i + 1} insufficient: ${result.text?.length || 0} characters`);
        }
      } catch (error) {
        console.log(`❌ Method ${i + 1} failed:`, error);
      }
    }

    throw new Error('All extraction methods failed');
  } catch (error) {
    console.error('PDF text extraction failed:', error);
    return {
      success: false,
      error: `Failed to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
      metadata: {
        fileType: 'PDF',
        fileName: file.name,
        fileSize: file.size,
        extractionMethod: 'Failed'
      }
    };
  }
}

/**
 * Main text extraction function that handles different file types
 */
export async function extractTextFromFile(file: File): Promise<TextExtractionResult> {
  const fileName = file.name.toLowerCase();
  const fileSize = file.size;

  console.log(`📁 Starting text extraction for: ${file.name} (${Math.round(fileSize / 1024)}KB)`);

  // File size validation
  if (fileSize > 10 * 1024 * 1024) { // 10MB limit
    return {
      success: false,
      error: 'File size exceeds 10MB limit. Please upload a smaller file.',
      metadata: {
        fileType: 'Oversized',
        fileName: file.name,
        fileSize
      }
    };
  }

  // Only accept PDF files
  if (!fileName.endsWith('.pdf')) {
    return {
      success: false,
      error: 'Only PDF files are supported. Please upload a PDF file.',
      metadata: {
        fileType: 'Unsupported',
        fileName: file.name,
        fileSize
      }
    };
  }

  return extractTextFromPDF(file);
}

/**
 * Validate and clean extracted text for quiz generation
 */
export function validateTextForQuiz(text: string): { isValid: boolean; error?: string; cleanText?: string } {
  if (!text || text.trim().length === 0) {
    return { isValid: false, error: 'No text content found' };
  }

  const cleanText = text.trim();

  if (cleanText.length < 100) {
    return { isValid: false, error: 'Text content is too short. Please provide at least 100 characters for meaningful quiz generation.' };
  }

  if (cleanText.length > 100000) {
    // For very long content, take smart excerpts from beginning, middle, and end
    const chunkSize = 30000;
    const beginning = cleanText.substring(0, chunkSize);
    const middle = cleanText.substring(Math.floor(cleanText.length / 2) - chunkSize / 2, Math.floor(cleanText.length / 2) + chunkSize / 2);
    const end = cleanText.substring(cleanText.length - chunkSize);

    const smartExcerpt = beginning + '\n\n[... content continues ...]\n\n' + middle + '\n\n[... content continues ...]\n\n' + end;

    return {
      isValid: true,
      cleanText: smartExcerpt,
      error: `Long document detected (${Math.round(cleanText.length / 1000)}k characters). Using smart excerpts from beginning, middle, and end for quiz generation.`
    };
  }

  return { isValid: true, cleanText };
}
