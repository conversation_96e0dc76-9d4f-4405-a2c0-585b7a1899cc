// Advanced PDF text extraction using multiple methods for maximum reliability
import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker with multiple fallbacks
const configureWorker = () => {
  const workerUrls = [
    `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`,
    `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`,
    `https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js`
  ];

  for (const url of workerUrls) {
    try {
      pdfjsLib.GlobalWorkerOptions.workerSrc = url;
      console.log(`✅ PDF.js worker configured with: ${url}`);
      break;
    } catch (error) {
      console.warn(`⚠️ Failed to set worker URL ${url}:`, error);
    }
  }
};

configureWorker();

export interface TextExtractionResult {
  success: boolean;
  text?: string;
  error?: string;
  metadata?: {
    fileType: string;
    fileName: string;
    fileSize: number;
    pageCount?: number;
    wordCount?: number;
    extractionMethod?: string;
  };
}

/**
 * Advanced text processing for PDF.js text content
 */
function processAdvancedTextContent(textContent: any): string {
  if (!textContent || !textContent.items) {
    return '';
  }

  const items = textContent.items;
  let processedText = '';
  let lastY = -1;
  let lastX = -1;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const text = item.str || '';
    
    if (!text.trim()) continue;

    // Get position information
    const transform = item.transform;
    const x = transform ? transform[4] : 0;
    const y = transform ? transform[5] : 0;

    // Add line breaks based on Y position changes (new lines)
    if (lastY !== -1 && Math.abs(y - lastY) > 5) {
      processedText += '\n';
    }
    // Add spaces based on X position changes (word spacing)
    else if (lastX !== -1 && x - lastX > 10) {
      processedText += ' ';
    }

    processedText += text;
    lastY = y;
    lastX = x + (item.width || 0);
  }

  return processedText;
}

/**
 * Advanced PDF.js extraction with proper text positioning and formatting
 */
async function extractWithAdvancedPDFJS(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  try {
    console.log('🔄 Starting advanced PDF.js extraction...');

    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      cMapUrl: `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/`,
      cMapPacked: true,
      standardFontDataUrl: `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/standard_fonts/`,
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 Advanced extraction - PDF loaded: ${pageCount} pages`);

  let extractedText = '';
  const maxPages = Math.min(pageCount, 150);

  for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      // Advanced text processing with positioning
      const pageText = processAdvancedTextContent(textContent);
      
      if (pageText.trim()) {
        extractedText += `${pageText}\n\n`;
      }
      
      // Progress logging
      if (pageNum % 10 === 0 || pageNum === maxPages) {
        console.log(`📖 Advanced: Processed ${pageNum}/${maxPages} pages...`);
      }
    } catch (pageError) {
      console.warn(`⚠️ Advanced: Error on page ${pageNum}:`, pageError);
    }
  }

  // Clean and process the extracted text
  let cleanText = extractedText
    .replace(/\n{3,}/g, '\n\n') // Limit consecutive line breaks
    .replace(/[ \t]+/g, ' ') // Normalize spaces and tabs
    .trim();

  if (pageCount > maxPages) {
    cleanText += `\n\n[Note: This PDF has ${pageCount} pages. Only the first ${maxPages} pages were processed.]`;
  }

  if (cleanText.length > 100) {
    const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
    console.log(`✅ Advanced extraction successful: ${wordCount} words from ${pageCount} pages`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        pageCount,
        wordCount,
        extractionMethod: 'Advanced PDF.js'
      }
    };
  }

  throw new Error('Advanced extraction: Insufficient text extracted');
  } catch (error) {
    console.error('❌ Advanced PDF.js extraction failed:', error);
    throw error;
  }
}

/**
 * Basic PDF.js extraction (fallback method)
 */
async function extractWithBasicPDFJS(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  try {
    console.log('🔄 Starting basic PDF.js extraction...');

    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;

    console.log(`📄 Basic extraction - PDF loaded: ${pageCount} pages`);

  let extractedText = '';
  const maxPages = Math.min(pageCount, 100);

  for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      
      // Simple text extraction
      const pageText = textContent.items
        .map((item: any) => item.str || '')
        .filter(str => str.trim())
        .join(' ');
      
      if (pageText.trim()) {
        extractedText += pageText + '\n\n';
      }
      
      if (pageNum % 20 === 0) {
        console.log(`📖 Basic: Processed ${pageNum}/${maxPages} pages...`);
      }
    } catch (pageError) {
      console.warn(`⚠️ Basic: Error on page ${pageNum}:`, pageError);
    }
  }

  let cleanText = extractedText
    .replace(/\s+/g, ' ')
    .trim();

  if (cleanText.length > 50) {
    const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
    console.log(`✅ Basic extraction successful: ${wordCount} words`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        pageCount,
        wordCount,
        extractionMethod: 'Basic PDF.js'
      }
    };
  }

  throw new Error('Basic extraction: Insufficient text extracted');
  } catch (error) {
    console.error('❌ Basic PDF.js extraction failed:', error);
    throw error;
  }
}

/**
 * Binary parsing extraction (last resort fallback)
 */
/**
 * Advanced OCR-like extraction using canvas rendering
 */
async function extractWithCanvasRendering(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  try {
    console.log('🔄 Starting canvas rendering extraction...');

    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      useSystemFonts: true,
      disableFontFace: false,
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 Canvas extraction - PDF loaded: ${pageCount} pages`);

    let extractedText = '';
    const maxPages = Math.min(pageCount, 50); // Limit for canvas rendering

    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);

        // Get text content first
        const textContent = await page.getTextContent();

        // Enhanced text processing with better spacing
        const pageText = processCanvasTextContent(textContent);

        if (pageText.trim()) {
          extractedText += `${pageText}\n\n`;
        }

        if (pageNum % 10 === 0) {
          console.log(`📖 Canvas: Processed ${pageNum}/${maxPages} pages...`);
        }
      } catch (pageError) {
        console.warn(`⚠️ Canvas: Error on page ${pageNum}:`, pageError);
      }
    }

    const cleanText = extractedText
      .replace(/\n{3,}/g, '\n\n')
      .replace(/[ \t]+/g, ' ')
      .trim();

    if (cleanText.length > 50) {
      const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
      console.log(`✅ Canvas extraction successful: ${wordCount} words`);

      return {
        success: true,
        text: cleanText,
        metadata: {
          fileType: 'PDF',
          fileName,
          fileSize: arrayBuffer.byteLength,
          pageCount,
          wordCount,
          extractionMethod: 'Canvas Rendering'
        }
      };
    }

    throw new Error('Canvas extraction: Insufficient text extracted');
  } catch (error) {
    console.error('❌ Canvas rendering extraction failed:', error);
    throw error;
  }
}

/**
 * Process text content with enhanced spacing and formatting
 */
function processCanvasTextContent(textContent: any): string {
  if (!textContent || !textContent.items) {
    return '';
  }

  const items = textContent.items;
  let processedText = '';
  let lastY = -1;
  let lastX = -1;
  let currentLine = '';

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const text = item.str || '';

    if (!text.trim()) continue;

    // Get position information
    const transform = item.transform;
    const x = transform ? transform[4] : 0;
    const y = transform ? transform[5] : 0;
    const fontSize = transform ? Math.abs(transform[0]) : 12;

    // Detect new lines based on Y position changes
    if (lastY !== -1 && Math.abs(y - lastY) > fontSize * 0.5) {
      if (currentLine.trim()) {
        processedText += currentLine.trim() + '\n';
      }
      currentLine = text;
    } else {
      // Same line - add spacing based on X position
      if (lastX !== -1 && x - lastX > fontSize * 0.3 && currentLine) {
        currentLine += ' ';
      }
      currentLine += text;
    }

    lastY = y;
    lastX = x + (item.width || 0);
  }

  // Add the last line
  if (currentLine.trim()) {
    processedText += currentLine.trim();
  }

  return processedText;
}

/**
 * Stream-based extraction for large PDFs
 */
async function extractWithStreamProcessing(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  try {
    console.log('🔄 Starting stream processing extraction...');

    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      useSystemFonts: false,
      disableFontFace: true,
      verbosity: 0
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 Stream extraction - PDF loaded: ${pageCount} pages`);

    let extractedText = '';
    const maxPages = Math.min(pageCount, 200);
    const batchSize = 5; // Process pages in batches

    for (let startPage = 1; startPage <= maxPages; startPage += batchSize) {
      const endPage = Math.min(startPage + batchSize - 1, maxPages);
      const batchPromises = [];

      for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        batchPromises.push(
          pdf.getPage(pageNum)
            .then(page => page.getTextContent())
            .then(textContent => {
              const pageText = textContent.items
                .map((item: any) => item.str || '')
                .filter((str: string) => str.trim())
                .join(' ');
              return pageText.trim() ? pageText + '\n\n' : '';
            })
            .catch(error => {
              console.warn(`⚠️ Stream: Error on page ${pageNum}:`, error);
              return '';
            })
        );
      }

      const batchResults = await Promise.all(batchPromises);
      extractedText += batchResults.join('');

      console.log(`📖 Stream: Processed batch ${startPage}-${endPage}/${maxPages}...`);
    }

    const cleanText = extractedText
      .replace(/\s+/g, ' ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    if (cleanText.length > 50) {
      const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
      console.log(`✅ Stream extraction successful: ${wordCount} words`);

      return {
        success: true,
        text: cleanText,
        metadata: {
          fileType: 'PDF',
          fileName,
          fileSize: arrayBuffer.byteLength,
          pageCount,
          wordCount,
          extractionMethod: 'Stream Processing'
        }
      };
    }

    throw new Error('Stream extraction: Insufficient text extracted');
  } catch (error) {
    console.error('❌ Stream processing extraction failed:', error);
    throw error;
  }
}

async function extractWithBinaryParsing(arrayBuffer: ArrayBuffer, fileName: string): Promise<TextExtractionResult> {
  try {
    console.log('🔄 Starting binary parsing extraction...');

    const uint8Array = new Uint8Array(arrayBuffer);
    let extractedText = '';
    let currentWord = '';

  // Look for readable text patterns in the binary data
  for (let i = 0; i < uint8Array.length; i++) {
    const byte = uint8Array[i];
    
    // Check for readable ASCII characters
    if ((byte >= 32 && byte <= 126) || byte === 10 || byte === 13) {
      const char = String.fromCharCode(byte);
      
      if (char.match(/[a-zA-Z0-9.,!?;:'"()\-\s]/)) {
        currentWord += char;
      } else {
        if (currentWord.trim().length > 2) {
          extractedText += currentWord + ' ';
        }
        currentWord = '';
      }
    } else {
      if (currentWord.trim().length > 2) {
        extractedText += currentWord + ' ';
      }
      currentWord = '';
    }
  }

  // Add the last word
  if (currentWord.trim().length > 2) {
    extractedText += currentWord;
  }

  // Clean and filter the text
  const words = extractedText.split(/\s+/);
  const filteredWords = words.filter(word => {
    return !word.match(/^(PDF|obj|endobj|stream|endstream|xref|trailer|BT|ET|Tf|Tj|TJ|Type|Page|Parent|Resources|MediaBox|Contents|Length|Filter|FlateDecode)$/i) &&
           word.length > 1 &&
           !word.match(/^[0-9.]+$/) &&
           !word.match(/^[\/\\<>{}%\[\]]+$/);
  });

  const cleanText = filteredWords.join(' ').trim();

  if (cleanText.length > 50) {
    const wordCount = filteredWords.length;
    console.log(`✅ Binary parsing successful: ${wordCount} words`);

    return {
      success: true,
      text: cleanText,
      metadata: {
        fileType: 'PDF',
        fileName,
        fileSize: arrayBuffer.byteLength,
        wordCount,
        extractionMethod: 'Binary Parsing'
      }
    };
  }

  throw new Error('Binary parsing: No readable text found');
  } catch (error) {
    console.error('❌ Binary parsing extraction failed:', error);
    throw error;
  }
}

/**
 * Analyze PDF characteristics to choose optimal extraction strategy
 */
async function analyzePDFCharacteristics(arrayBuffer: ArrayBuffer): Promise<{
  pageCount: number;
  hasComplexLayout: boolean;
  hasImages: boolean;
  estimatedTextDensity: number;
  recommendedMethod: string;
}> {
  try {
    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;

    // Analyze first few pages to determine characteristics
    const samplePages = Math.min(3, pageCount);
    let totalTextItems = 0;
    let hasComplexLayout = false;
    let hasImages = false;

    for (let pageNum = 1; pageNum <= samplePages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        totalTextItems += textContent.items.length;

        // Check for complex layout (many small text items)
        if (textContent.items.length > 100) {
          hasComplexLayout = true;
        }

        // Check for images (simplified check)
        const operatorList = await page.getOperatorList();
        if (operatorList.fnArray.includes(pdfjsLib.OPS.paintImageXObject)) {
          hasImages = true;
        }
      } catch (error) {
        console.warn(`Analysis error on page ${pageNum}:`, error);
      }
    }

    const estimatedTextDensity = totalTextItems / samplePages;

    // Recommend extraction method based on analysis
    let recommendedMethod = 'advanced';
    if (pageCount > 100) {
      recommendedMethod = 'stream';
    } else if (hasComplexLayout && estimatedTextDensity > 150) {
      recommendedMethod = 'canvas';
    } else if (estimatedTextDensity < 20) {
      recommendedMethod = 'binary';
    }

    console.log(`📊 PDF Analysis: ${pageCount} pages, density: ${estimatedTextDensity.toFixed(1)}, complex: ${hasComplexLayout}, images: ${hasImages}, recommended: ${recommendedMethod}`);

    return {
      pageCount,
      hasComplexLayout,
      hasImages,
      estimatedTextDensity,
      recommendedMethod
    };
  } catch (error) {
    console.warn('PDF analysis failed, using default strategy:', error);
    return {
      pageCount: 0,
      hasComplexLayout: false,
      hasImages: false,
      estimatedTextDensity: 0,
      recommendedMethod: 'advanced'
    };
  }
}

/**
 * Smart PDF text extraction with adaptive strategy selection
 */
export async function extractTextFromPDF(file: File): Promise<TextExtractionResult> {
  console.log('🚀 Starting smart PDF text extraction for:', file.name);

  try {
    const arrayBuffer = await file.arrayBuffer();

    // Analyze PDF to choose optimal extraction strategy
    const analysis = await analyzePDFCharacteristics(arrayBuffer);

    // Define extraction methods with priorities based on analysis
    const getMethodsByPriority = (recommended: string) => {
      const allMethods = {
        advanced: () => extractWithAdvancedPDFJS(arrayBuffer, file.name),
        canvas: () => extractWithCanvasRendering(arrayBuffer, file.name),
        stream: () => extractWithStreamProcessing(arrayBuffer, file.name),
        basic: () => extractWithBasicPDFJS(arrayBuffer, file.name),
        binary: () => extractWithBinaryParsing(arrayBuffer, file.name)
      };

      // Prioritize recommended method
      const methods = [allMethods[recommended as keyof typeof allMethods]];

      // Add other methods as fallbacks
      Object.entries(allMethods).forEach(([key, method]) => {
        if (key !== recommended) {
          methods.push(method);
        }
      });

      return methods.filter(Boolean);
    };

    const methods = getMethodsByPriority(analysis.recommendedMethod);

    // Adaptive threshold based on file size
    const minTextLength = Math.max(50, Math.min(500, file.size / 10000));
    console.log(`📏 Using adaptive text threshold: ${minTextLength} characters`);

    let bestResult: TextExtractionResult | null = null;
    let bestScore = 0;

    for (let i = 0; i < methods.length; i++) {
      try {
        const methodName = ['Advanced PDF.js', 'Canvas Rendering', 'Stream Processing', 'Basic PDF.js', 'Binary Parsing'][i] || `Method ${i + 1}`;
        console.log(`🔄 Trying ${methodName}...`);

        const startTime = Date.now();
        const result = await methods[i]();
        const duration = Date.now() - startTime;

        if (result.success && result.text) {
          const textLength = result.text.length;
          const wordCount = result.metadata?.wordCount || 0;

          // Calculate quality score based on text length, word count, and extraction speed
          const lengthScore = Math.min(textLength / 1000, 10);
          const wordScore = Math.min(wordCount / 100, 10);
          const speedScore = Math.max(0, 10 - duration / 1000);
          const qualityScore = lengthScore + wordScore + speedScore;

          console.log(`📊 ${methodName}: ${textLength} chars, ${wordCount} words, ${duration}ms, score: ${qualityScore.toFixed(1)}`);

          // If this result meets minimum requirements and is better than previous
          if (textLength >= minTextLength && qualityScore > bestScore) {
            bestResult = result;
            bestScore = qualityScore;

            // If we have a really good result, use it immediately
            if (qualityScore > 15 && textLength > 1000) {
              console.log(`✅ Excellent result found with ${methodName}, stopping search`);
              return result;
            }
          }
        } else {
          console.log(`⚠️ ${methodName} insufficient: ${result.text?.length || 0} characters`);
        }
      } catch (error) {
        const methodName = ['Advanced PDF.js', 'Canvas Rendering', 'Stream Processing', 'Basic PDF.js', 'Binary Parsing'][i] || `Method ${i + 1}`;
        console.log(`❌ ${methodName} failed:`, error);
      }
    }

    // Return best result if we found one
    if (bestResult) {
      console.log(`✅ Using best result with score: ${bestScore.toFixed(1)}`);
      return bestResult;
    }

    throw new Error('All extraction methods failed');
  } catch (error) {
    console.error('PDF text extraction failed:', error);
    return {
      success: false,
      error: `Failed to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
      metadata: {
        fileType: 'PDF',
        fileName: file.name,
        fileSize: file.size,
        extractionMethod: 'Failed'
      }
    };
  }
}

/**
 * Main text extraction function that handles different file types
 */
export async function extractTextFromFile(file: File): Promise<TextExtractionResult> {
  const fileName = file.name.toLowerCase();
  const fileSize = file.size;

  console.log(`📁 Starting text extraction for: ${file.name} (${Math.round(fileSize / 1024)}KB)`);

  // File size validation
  if (fileSize > 10 * 1024 * 1024) { // 10MB limit
    return {
      success: false,
      error: 'File size exceeds 10MB limit. Please upload a smaller file.',
      metadata: {
        fileType: 'Oversized',
        fileName: file.name,
        fileSize
      }
    };
  }

  // Only accept PDF files
  if (!fileName.endsWith('.pdf')) {
    return {
      success: false,
      error: 'Only PDF files are supported. Please upload a PDF file.',
      metadata: {
        fileType: 'Unsupported',
        fileName: file.name,
        fileSize
      }
    };
  }

  return extractTextFromPDF(file);
}

/**
 * Advanced text quality analysis and enhancement
 */
function enhanceExtractedText(text: string): string {
  if (!text) return text;

  console.log('🔧 Enhancing extracted text quality...');

  let enhanced = text;

  // Fix common extraction issues
  enhanced = enhanced
    // Fix broken words (letters separated by spaces)
    .replace(/\b([a-z])\s+([a-z])\s+([a-z])/gi, '$1$2$3')
    // Fix missing spaces after periods
    .replace(/\.([A-Z])/g, '. $1')
    // Fix multiple spaces
    .replace(/\s{2,}/g, ' ')
    // Fix broken sentences
    .replace(/([a-z])\s*\n\s*([a-z])/g, '$1 $2')
    // Fix paragraph breaks
    .replace(/\n{3,}/g, '\n\n')
    // Remove excessive punctuation
    .replace(/[.]{3,}/g, '...')
    // Fix common OCR errors
    .replace(/\b0\b/g, 'O') // Zero to O
    .replace(/\bl\b/g, 'I') // lowercase l to I
    .replace(/rn/g, 'm') // rn to m
    .trim();

  // Calculate improvement metrics
  const originalWords = text.split(/\s+/).length;
  const enhancedWords = enhanced.split(/\s+/).length;
  const improvement = ((enhancedWords - originalWords) / originalWords * 100).toFixed(1);

  console.log(`✨ Text enhancement: ${originalWords} → ${enhancedWords} words (${improvement}% change)`);

  return enhanced;
}

/**
 * Comprehensive text quality analysis
 */
function analyzeTextQuality(text: string): {
  score: number;
  issues: string[];
  suggestions: string[];
  isReadable: boolean;
} {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Basic metrics
  const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
  const charCount = text.length;
  const avgWordLength = charCount / wordCount;
  const sentenceCount = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const avgSentenceLength = wordCount / sentenceCount;

  // Quality scoring
  let score = 100;

  // Check word count
  if (wordCount < 50) {
    score -= 30;
    issues.push('Very short text');
    suggestions.push('Text may be too short for meaningful analysis');
  } else if (wordCount < 100) {
    score -= 15;
    issues.push('Short text');
  }

  // Check average word length
  if (avgWordLength < 3) {
    score -= 20;
    issues.push('Very short average word length');
    suggestions.push('Text may contain extraction artifacts');
  } else if (avgWordLength > 8) {
    score -= 10;
    issues.push('Very long average word length');
  }

  // Check sentence structure
  if (avgSentenceLength < 5) {
    score -= 15;
    issues.push('Very short sentences');
  } else if (avgSentenceLength > 30) {
    score -= 10;
    issues.push('Very long sentences');
  }

  // Check for extraction artifacts
  const digitRatio = (text.match(/\d/g) || []).length / charCount;
  if (digitRatio > 0.3) {
    score -= 25;
    issues.push('High digit ratio - possible extraction artifacts');
    suggestions.push('Consider using a different extraction method');
  }

  // Check for repeated characters
  const repeatedChars = text.match(/(.)\1{3,}/g) || [];
  if (repeatedChars.length > 0) {
    score -= 15;
    issues.push('Repeated character sequences detected');
  }

  // Check readability
  const isReadable = score >= 60 && wordCount >= 20 && avgWordLength >= 3 && avgWordLength <= 8;

  return {
    score: Math.max(0, score),
    issues,
    suggestions,
    isReadable
  };
}

/**
 * Enhanced text validation for quiz generation with quality analysis
 */
export function validateTextForQuiz(text: string): { isValid: boolean; error?: string; cleanText?: string; quality?: any } {
  if (!text || text.trim().length === 0) {
    return { isValid: false, error: 'No text content found' };
  }

  // Enhance text quality
  const enhancedText = enhanceExtractedText(text);

  // Analyze text quality
  const quality = analyzeTextQuality(enhancedText);

  const cleanText = enhancedText.trim();

  if (cleanText.length < 100) {
    return {
      isValid: false,
      error: 'Text content is too short. Please provide at least 100 characters for meaningful quiz generation.',
      quality
    };
  }

  // Quality-based validation
  if (!quality.isReadable) {
    return {
      isValid: false,
      error: `Text quality is too low for quiz generation. Issues: ${quality.issues.join(', ')}. Suggestions: ${quality.suggestions.join(', ')}`,
      quality
    };
  }

  if (cleanText.length > 100000) {
    // For very long content, take smart excerpts from beginning, middle, and end
    const chunkSize = 30000;
    const beginning = cleanText.substring(0, chunkSize);
    const middle = cleanText.substring(Math.floor(cleanText.length / 2) - chunkSize / 2, Math.floor(cleanText.length / 2) + chunkSize / 2);
    const end = cleanText.substring(cleanText.length - chunkSize);

    const smartExcerpt = beginning + '\n\n[... content continues ...]\n\n' + middle + '\n\n[... content continues ...]\n\n' + end;

    return {
      isValid: true,
      cleanText: smartExcerpt,
      quality,
      error: `Long document detected (${Math.round(cleanText.length / 1000)}k characters). Using smart excerpts from beginning, middle, and end for quiz generation.`
    };
  }

  return {
    isValid: true,
    cleanText,
    quality
  };
}
