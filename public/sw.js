// StudyFam Service Worker for Offline Mode & Push Notifications
const CACHE_NAME = 'studyfam-offline-v2';
const OFFLINE_URL = '/offline.html';

// Static assets to cache for offline use (Vite structure)
const urlsToCache = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  '/logo192.png',
  '/logo512.png'
];

// API endpoints that should work offline
const OFFLINE_CAPABLE_APIS = [
  '/api/notes',
  '/api/timetable',
  '/api/messages',
  '/api/groups',
  '/api/past-papers'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets for offline use');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        // Force the waiting service worker to become active
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Enhanced fetch event for offline support
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle navigation requests (page loads)
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Cache successful page loads
          const responseClone = response.clone();
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(request, responseClone);
            });
          return response;
        })
        .catch(() => {
          // If offline, serve from cache or offline page
          return caches.match(request)
            .then((cachedResponse) => {
              return cachedResponse || caches.match('/');
            });
        })
    );
    return;
  }

  // Handle API requests with offline fallback
  if (url.pathname.startsWith('/api/') || url.hostname.includes('supabase')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Cache successful GET requests
          if (response.ok && request.method === 'GET') {
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(request, responseClone);
              });
          }
          return response;
        })
        .catch(() => {
          // If offline, try cache first, then return offline indicator
          return caches.match(request)
            .then((cachedResponse) => {
              if (cachedResponse) {
                return cachedResponse;
              }

              // Return offline indicator for API calls
              return new Response(
                JSON.stringify({
                  offline: true,
                  error: 'No internet connection',
                  timestamp: Date.now(),
                  cached: false
                }),
                {
                  status: 503,
                  statusText: 'Service Unavailable',
                  headers: { 'Content-Type': 'application/json' }
                }
              );
            });
        })
    );
    return;
  }

  // Handle other requests (static assets) - simplified to avoid errors
  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(request)
          .then((response) => {
            // Only cache successful responses for specific file types
            if (response.ok && (
              request.url.includes('.js') ||
              request.url.includes('.css') ||
              request.url.includes('.png') ||
              request.url.includes('.ico') ||
              request.url.includes('.json')
            )) {
              const responseClone = response.clone();
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(request, responseClone);
                })
                .catch(() => {
                  // Silently ignore cache errors
                });
            }
            return response;
          })
          .catch(() => {
            // If fetch fails, just return a basic response to avoid errors
            return new Response('', { status: 404 });
          });
      })
  );
});

// Push event for notifications
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  let notificationData = {
    title: 'StudyFam',
    body: 'You have a new notification',
    icon: '/logo192.png',
    badge: '/favicon.ico',
    tag: 'studyfam-notification',
    requireInteraction: false,
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/favicon.ico'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };

  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = {
        ...notificationData,
        ...data
      };
    } catch (e) {
      console.error('Error parsing push data:', e);
      notificationData.body = event.data.text();
    }
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Default action or 'view' action
  const urlToOpen = event.notification.data?.url || '/notifications';
  
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Background sync for offline data synchronization
self.addEventListener('sync', (event) => {
  console.log('Background sync event:', event.tag);

  if (event.tag === 'sync-offline-data') {
    event.waitUntil(syncOfflineData());
  } else if (event.tag === 'sync-notes') {
    event.waitUntil(syncNotes());
  } else if (event.tag === 'sync-messages') {
    event.waitUntil(syncMessages());
  } else if (event.tag === 'sync-timetable') {
    event.waitUntil(syncTimetable());
  }
});

// Sync offline data when connection is restored
async function syncOfflineData() {
  try {
    console.log('Syncing offline data...');

    // Get offline data from IndexedDB (will be implemented)
    const offlineData = await getOfflineDataFromIDB();

    if (offlineData && offlineData.length > 0) {
      // Sync with server
      await syncWithServer(offlineData);

      // Clear synced data from local storage
      await clearSyncedData();

      // Notify all clients that sync is complete
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'SYNC_COMPLETE',
          data: { synced: offlineData.length }
        });
      });
    }

  } catch (error) {
    console.error('Offline data sync failed:', error);
  }
}

// Individual sync functions
async function syncNotes() {
  console.log('Syncing notes...');
  // Implementation for notes sync
}

async function syncMessages() {
  console.log('Syncing messages...');
  // Implementation for messages sync
}

async function syncTimetable() {
  console.log('Syncing timetable...');
  // Implementation for timetable sync
}

// Helper functions (to be implemented with IndexedDB)
async function getOfflineDataFromIDB() {
  // Will be implemented with IndexedDB operations
  return [];
}

async function syncWithServer(data) {
  // Will sync data with Supabase when online
  console.log('Syncing with server:', data);
}

async function clearSyncedData() {
  // Clear successfully synced data from IndexedDB
  console.log('Clearing synced data');
}

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});


