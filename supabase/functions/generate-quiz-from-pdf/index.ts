
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { getDocument } from 'https://esm.sh/pdfjs-dist@3.11.174'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

/**
 * Advanced PDF text extraction using PDF.js in Deno
 */
async function extractTextFromPDFAdvanced(pdfBuffer: ArrayBuffer): Promise<string> {
  try {
    console.log('🔄 Starting advanced PDF text extraction...');

    // Load PDF document
    const loadingTask = getDocument({
      data: new Uint8Array(pdfBuffer),
      useSystemFonts: true,
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 PDF loaded: ${pageCount} pages`);

    let extractedText = '';
    const maxPages = Math.min(pageCount, 200); // Increased limit for server-side

    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Process text items with positioning
        const pageText = processTextContentAdvanced(textContent);

        if (pageText.trim()) {
          extractedText += `${pageText}\n\n`;
        }

        // Progress logging every 20 pages
        if (pageNum % 20 === 0) {
          console.log(`📖 Advanced: Processed ${pageNum}/${maxPages} pages...`);
        }
      } catch (pageError) {
        console.warn(`⚠️ Error on page ${pageNum}:`, pageError);
      }
    }

    // Clean the extracted text
    const cleanText = extractedText
      .replace(/\n{3,}/g, '\n\n') // Limit consecutive line breaks
      .replace(/[ \t]+/g, ' ') // Normalize spaces
      .trim();

    console.log(`✅ Advanced extraction completed: ${cleanText.length} characters`);
    return cleanText;
  } catch (error) {
    console.error('❌ Advanced PDF extraction failed:', error);
    throw error;
  }
}

/**
 * Process text content with advanced positioning logic
 */
function processTextContentAdvanced(textContent: any): string {
  if (!textContent || !textContent.items) {
    return '';
  }

  const items = textContent.items;
  let processedText = '';
  let lastY = -1;
  let lastX = -1;
  let currentLine = '';

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const text = item.str || '';

    if (!text.trim()) continue;

    // Get position information
    const transform = item.transform;
    const x = transform ? transform[4] : 0;
    const y = transform ? transform[5] : 0;

    // Detect new lines based on Y position changes
    if (lastY !== -1 && Math.abs(y - lastY) > 5) {
      if (currentLine.trim()) {
        processedText += currentLine.trim() + '\n';
      }
      currentLine = text;
    } else {
      // Same line - add spacing if needed
      if (lastX !== -1 && x - lastX > 10 && currentLine) {
        currentLine += ' ';
      }
      currentLine += text;
    }

    lastY = y;
    lastX = x + (item.width || 0);
  }

  // Add the last line
  if (currentLine.trim()) {
    processedText += currentLine.trim();
  }

  return processedText;
}

interface QuizGenerationRequest {
  pdfContent: string;
  numQuestions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionTypes?: ('multiple-choice' | 'true-false' | 'short-answer')[];
  subject?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client to verify user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the request body
    const { 
      pdfContent, 
      numQuestions = 10, 
      difficulty = 'medium',
      questionTypes = ['multiple-choice'],
      subject = 'general'
    }: QuizGenerationRequest = await req.json()

    if (!pdfContent || pdfContent.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'PDF content is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenRouter API key from environment
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      return new Response(
        JSON.stringify({ error: 'OpenRouter API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Create prompt for quiz generation
    const systemPrompt = `You are an expert quiz generator. Generate ${numQuestions} ${difficulty} level quiz questions based on the provided content.

Question types to include: ${questionTypes.join(', ')}.

For multiple-choice questions, provide 4 options (A, B, C, D).
For true-false questions, the answer should be either "True" or "False".
For short-answer questions, provide a concise correct answer.

Always include an explanation for each answer.

Return the response as a JSON object with this structure:
{
  "title": "Generated Quiz Title",
  "questions": [
    {
      "question": "Question text",
      "type": "multiple-choice|true-false|short-answer",
      "options": ["A", "B", "C", "D"] (only for multiple-choice),
      "correctAnswer": "Correct answer",
      "explanation": "Why this is the correct answer"
    }
  ]
}

Focus on key concepts, important facts, and understanding rather than trivial details.`

    // Handle long content by taking a smart excerpt
    let contentToProcess = pdfContent;
    if (pdfContent.length > 15000) {
      // Take first 7500 chars and last 7500 chars to capture beginning and end
      const firstPart = pdfContent.substring(0, 7500);
      const lastPart = pdfContent.substring(pdfContent.length - 7500);
      contentToProcess = firstPart + '\n\n[... content truncated ...]\n\n' + lastPart;
      console.log(`Content truncated from ${pdfContent.length} to ${contentToProcess.length} characters`);
    }

    const userPrompt = `Generate a quiz from this content:\n\n${contentToProcess}`

    // Call OpenRouter API
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://study-fam.com',
        'X-Title': 'Study Fam Quiz Generator',
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 4000, // Increased for longer quizzes
      }),
    })

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.json()
      console.error('OpenRouter API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to generate quiz questions' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const data = await openRouterResponse.json()
    const generatedContent = data.choices[0]?.message?.content

    if (!generatedContent) {
      return new Response(
        JSON.stringify({ error: 'No quiz generated' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the JSON response
    let quizData;
    try {
      quizData = JSON.parse(generatedContent);
    } catch (parseError) {
      console.error('Failed to parse quiz JSON:', parseError);
      return new Response(
        JSON.stringify({ error: 'Failed to parse generated quiz' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Log the generation for analytics
    try {
      await supabaseClient
        .from('ai_tutor_logs')
        .insert({
          user_id: user.id,
          subject: 'quiz-generation',
          message_count: 1,
          response_length: generatedContent.length,
          created_at: new Date().toISOString()
        })
      console.log('Quiz generation logged successfully')
    } catch (logError) {
      console.warn('Failed to log quiz generation:', logError)
    }

    // Return the generated quiz
    return new Response(
      JSON.stringify({
        quiz: quizData,
        usage: data.usage
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
