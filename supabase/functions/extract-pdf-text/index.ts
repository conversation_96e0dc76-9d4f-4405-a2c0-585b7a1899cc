import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { getDocument } from 'https://esm.sh/pdfjs-dist@3.11.174'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

/**
 * Advanced PDF text extraction using PDF.js in Deno
 */
async function extractTextFromPDFAdvanced(pdfBuffer: ArrayBuffer): Promise<string> {
  try {
    console.log('🔄 Starting advanced PDF text extraction...');
    
    // Load PDF document
    const loadingTask = getDocument({
      data: new Uint8Array(pdfBuffer),
      useSystemFonts: true,
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 PDF loaded: ${pageCount} pages`);

    let extractedText = '';
    const maxPages = Math.min(pageCount, 200); // Server can handle more pages

    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Process text items with positioning
        const pageText = processTextContentAdvanced(textContent);
        
        if (pageText.trim()) {
          extractedText += `${pageText}\n\n`;
        }
        
        // Progress logging every 20 pages
        if (pageNum % 20 === 0) {
          console.log(`📖 Advanced: Processed ${pageNum}/${maxPages} pages...`);
        }
      } catch (pageError) {
        console.warn(`⚠️ Error on page ${pageNum}:`, pageError);
      }
    }

    // Clean the extracted text
    const cleanText = extractedText
      .replace(/\n{3,}/g, '\n\n') // Limit consecutive line breaks
      .replace(/[ \t]+/g, ' ') // Normalize spaces
      .replace(/\s+([.!?])/g, '$1') // Fix punctuation spacing
      .replace(/([.!?])\s*([A-Z])/g, '$1 $2') // Ensure space after sentences
      .trim();

    if (pageCount > maxPages) {
      return cleanText + `\n\n[Note: This PDF has ${pageCount} pages. Only the first ${maxPages} pages were processed.]`;
    }

    console.log(`✅ Advanced extraction completed: ${cleanText.length} characters`);
    return cleanText;
  } catch (error) {
    console.error('❌ Advanced PDF extraction failed:', error);
    throw error;
  }
}

/**
 * Process text content with advanced positioning logic
 */
function processTextContentAdvanced(textContent: any): string {
  if (!textContent || !textContent.items) {
    return '';
  }

  const items = textContent.items;
  let processedText = '';
  let lastY = -1;
  let lastX = -1;
  let currentLine = '';

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const text = item.str || '';
    
    if (!text.trim()) continue;

    // Get position information
    const transform = item.transform;
    const x = transform ? transform[4] : 0;
    const y = transform ? transform[5] : 0;

    // Detect new lines based on Y position changes
    if (lastY !== -1 && Math.abs(y - lastY) > 5) {
      if (currentLine.trim()) {
        processedText += currentLine.trim() + '\n';
      }
      currentLine = text;
    } else {
      // Same line - add spacing if needed
      if (lastX !== -1 && x - lastX > 10 && currentLine) {
        currentLine += ' ';
      }
      currentLine += text;
    }

    lastY = y;
    lastX = x + (item.width || 0);
  }

  // Add the last line
  if (currentLine.trim()) {
    processedText += currentLine.trim();
  }

  return processedText;
}

/**
 * Fallback PDF text extraction (basic method)
 */
async function extractTextFromPDFBasic(pdfBuffer: ArrayBuffer): Promise<string> {
  try {
    console.log('🔄 Starting basic PDF text extraction...');
    
    const loadingTask = getDocument({
      data: new Uint8Array(pdfBuffer),
    });

    const pdf = await loadingTask.promise;
    const pageCount = pdf.numPages;
    console.log(`📄 Basic extraction - PDF loaded: ${pageCount} pages`);

    let extractedText = '';
    const maxPages = Math.min(pageCount, 100);

    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        // Simple text extraction
        const pageText = textContent.items
          .map((item: any) => item.str || '')
          .filter((str: string) => str.trim())
          .join(' ');
        
        if (pageText.trim()) {
          extractedText += pageText + '\n\n';
        }
      } catch (pageError) {
        console.warn(`⚠️ Basic: Error on page ${pageNum}:`, pageError);
      }
    }

    const cleanText = extractedText
      .replace(/\s+/g, ' ')
      .trim();

    console.log(`✅ Basic extraction completed: ${cleanText.length} characters`);
    return cleanText;
  } catch (error) {
    console.error('❌ Basic PDF extraction failed:', error);
    throw error;
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify authentication
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { data: { user } } = await supabaseClient.auth.getUser()

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get PDF file from request
    const formData = await req.formData()
    const pdfFile = formData.get('pdf') as File

    if (!pdfFile) {
      return new Response(
        JSON.stringify({ error: 'PDF file is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Convert file to ArrayBuffer
    const pdfBuffer = await pdfFile.arrayBuffer()
    console.log(`📁 Processing PDF: ${pdfFile.name} (${Math.round(pdfBuffer.byteLength / 1024)}KB)`)

    // Try advanced extraction first, then fallback to basic
    let extractedText: string
    let extractionMethod: string

    try {
      extractedText = await extractTextFromPDFAdvanced(pdfBuffer)
      extractionMethod = 'Advanced PDF.js'
    } catch (advancedError) {
      console.warn('⚠️ Advanced extraction failed, trying basic method:', advancedError)
      try {
        extractedText = await extractTextFromPDFBasic(pdfBuffer)
        extractionMethod = 'Basic PDF.js'
      } catch (basicError) {
        console.error('❌ All extraction methods failed:', basicError)
        return new Response(
          JSON.stringify({ 
            error: 'Failed to extract text from PDF. The file may be corrupted, password-protected, or contain only images.',
            details: basicError.message
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Validate extracted text
    if (!extractedText || extractedText.trim().length < 50) {
      return new Response(
        JSON.stringify({ 
          error: 'Insufficient text extracted from PDF. The file may contain only images or be poorly formatted.',
          extractedLength: extractedText?.length || 0
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length

    console.log(`✅ PDF text extraction successful: ${wordCount} words using ${extractionMethod}`)

    return new Response(
      JSON.stringify({
        success: true,
        text: extractedText,
        metadata: {
          fileName: pdfFile.name,
          fileSize: pdfBuffer.byteLength,
          wordCount,
          extractionMethod,
          characterCount: extractedText.length
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ PDF extraction error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error during PDF processing',
        details: error.message
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
