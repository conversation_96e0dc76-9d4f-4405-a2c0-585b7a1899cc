-- Comprehensive cleanup of payment and subscription system
-- This migration removes all payment/subscription related tables, functions, and policies
-- Run this migration to completely remove the payment system

BEGIN;

-- Drop RLS policies first (if they exist)
DROP POLICY IF EXISTS "Anyone can view active subscription plans" ON public.subscription_plans;
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can view their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Users can insert their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Users can update their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Service role can manage subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Service role can manage transactions" ON public.payment_transactions;

-- Drop functions (if they exist)
DROP FUNCTION IF EXISTS public.start_free_trial(UUI<PERSON>, UUI<PERSON>);
DROP FUNCTION IF EXISTS public.check_subscription_status(UUID);
DROP FUNCTION IF EXISTS public.update_subscription_status();
DROP FUNCTION IF EXISTS public.handle_subscription_change();
DROP FUNCTION IF EXISTS public.get_user_subscription_status(UUID);
DROP FUNCTION IF EXISTS public.activate_subscription(UUID, UUID);

-- Drop triggers (if they exist)
DROP TRIGGER IF EXISTS subscription_updated_at_trigger ON public.user_subscriptions;
DROP TRIGGER IF EXISTS subscription_plans_updated_at_trigger ON public.subscription_plans;
DROP TRIGGER IF EXISTS payment_transactions_updated_at_trigger ON public.payment_transactions;
DROP TRIGGER IF EXISTS woocommerce_orders_updated_at_trigger ON public.woocommerce_orders;

-- Drop tables in correct order (foreign key dependencies)
DROP TABLE IF EXISTS public.payment_transactions CASCADE;
DROP TABLE IF EXISTS public.user_subscriptions CASCADE;
DROP TABLE IF EXISTS public.subscription_plans CASCADE;
DROP TABLE IF EXISTS public.woocommerce_orders CASCADE;

-- Drop any remaining sequences
DROP SEQUENCE IF EXISTS subscription_plans_id_seq CASCADE;
DROP SEQUENCE IF EXISTS user_subscriptions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payment_transactions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS woocommerce_orders_id_seq CASCADE;

-- Drop custom types (if they exist)
DROP TYPE IF EXISTS subscription_status CASCADE;
DROP TYPE IF EXISTS payment_status CASCADE;
DROP TYPE IF EXISTS transaction_type CASCADE;

-- Drop indexes (if they exist)
DROP INDEX IF EXISTS idx_user_subscriptions_user_id;
DROP INDEX IF EXISTS idx_user_subscriptions_status;
DROP INDEX IF EXISTS idx_payment_transactions_user_id;
DROP INDEX IF EXISTS idx_payment_transactions_reference;
DROP INDEX IF EXISTS idx_subscription_plans_active;
DROP INDEX IF EXISTS idx_woocommerce_orders_user_id;

-- Drop views (if they exist)
DROP VIEW IF EXISTS active_subscriptions CASCADE;
DROP VIEW IF EXISTS subscription_summary CASCADE;
DROP VIEW IF EXISTS user_payment_history CASCADE;

COMMIT;

-- Log the cleanup
INSERT INTO public.migration_log (migration_name, executed_at, description) 
VALUES (
  '20250120000000_cleanup_payment_system', 
  NOW(), 
  'Removed all payment and subscription system tables, functions, and policies'
) ON CONFLICT DO NOTHING;
