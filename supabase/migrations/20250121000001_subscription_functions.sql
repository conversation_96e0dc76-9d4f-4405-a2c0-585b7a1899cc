-- Create subscription system functions and policies
-- This migration adds all the business logic for the subscription system

BEGIN;

-- Function to start free trial for new users
CREATE OR REPLACE FUNCTION public.start_free_trial(p_user_id UUID)
RETURNS UUID AS $$
DECLARE
    v_plan_id UUID;
    v_subscription_id UUID;
    v_trial_start TIMESTAMP WITH TIME ZONE;
    v_trial_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get the default plan
    SELECT id INTO v_plan_id 
    FROM public.subscription_plans 
    WHERE is_active = true 
    ORDER BY created_at ASC 
    LIMIT 1;
    
    IF v_plan_id IS NULL THEN
        RAISE EXCEPTION 'No active subscription plan found';
    END IF;
    
    -- Check if user already has a subscription
    IF EXISTS (SELECT 1 FROM public.user_subscriptions WHERE user_id = p_user_id) THEN
        RAISE EXCEPTION 'User already has a subscription';
    END IF;
    
    -- Set trial dates
    v_trial_start := NOW();
    v_trial_end := v_trial_start + INTERVAL '7 days';
    
    -- Create trial subscription
    INSERT INTO public.user_subscriptions (
        user_id,
        plan_id,
        status,
        trial_start,
        trial_end,
        current_period_start,
        current_period_end,
        next_payment_due
    ) VALUES (
        p_user_id,
        v_plan_id,
        'trialing',
        v_trial_start,
        v_trial_end,
        v_trial_start,
        v_trial_end,
        v_trial_end
    ) RETURNING id INTO v_subscription_id;
    
    RETURN v_subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has active subscription or trial
CREATE OR REPLACE FUNCTION public.user_has_active_access(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_has_access BOOLEAN := false;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM public.user_subscriptions 
        WHERE user_id = p_user_id 
        AND status IN ('trialing', 'active')
        AND (
            (status = 'trialing' AND trial_end > NOW()) OR
            (status = 'active' AND current_period_end > NOW())
        )
    ) INTO v_has_access;
    
    RETURN v_has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user subscription status
CREATE OR REPLACE FUNCTION public.get_user_subscription_status(p_user_id UUID)
RETURNS TABLE (
    subscription_id UUID,
    status TEXT,
    trial_days_remaining INTEGER,
    days_until_renewal INTEGER,
    is_trial BOOLEAN,
    plan_name TEXT,
    features JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        us.id,
        us.status,
        CASE 
            WHEN us.status = 'trialing' AND us.trial_end > NOW() 
            THEN EXTRACT(DAY FROM us.trial_end - NOW())::INTEGER
            ELSE 0
        END as trial_days_remaining,
        CASE 
            WHEN us.status = 'active' AND us.current_period_end > NOW()
            THEN EXTRACT(DAY FROM us.current_period_end - NOW())::INTEGER
            ELSE 0
        END as days_until_renewal,
        (us.status = 'trialing') as is_trial,
        sp.name as plan_name,
        sp.features
    FROM public.user_subscriptions us
    JOIN public.subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = p_user_id
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to convert trial to paid subscription
CREATE OR REPLACE FUNCTION public.convert_trial_to_paid(
    p_user_id UUID,
    p_payment_reference TEXT,
    p_payment_provider TEXT,
    p_payment_method TEXT
)
RETURNS UUID AS $$
DECLARE
    v_subscription_id UUID;
    v_plan_id UUID;
    v_period_start TIMESTAMP WITH TIME ZONE;
    v_period_end TIMESTAMP WITH TIME ZONE;
    v_transaction_id UUID;
BEGIN
    -- Get current trial subscription
    SELECT id, plan_id INTO v_subscription_id, v_plan_id
    FROM public.user_subscriptions 
    WHERE user_id = p_user_id 
    AND status = 'trialing'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF v_subscription_id IS NULL THEN
        RAISE EXCEPTION 'No active trial found for user';
    END IF;
    
    -- Set new billing period
    v_period_start := NOW();
    v_period_end := v_period_start + INTERVAL '1 month';
    
    -- Update subscription to active
    UPDATE public.user_subscriptions 
    SET 
        status = 'active',
        current_period_start = v_period_start,
        current_period_end = v_period_end,
        next_payment_due = v_period_end,
        last_payment_date = v_period_start,
        payment_method = p_payment_method,
        payment_provider = p_payment_provider,
        payment_reference = p_payment_reference,
        updated_at = NOW()
    WHERE id = v_subscription_id;
    
    -- Create payment transaction record
    INSERT INTO public.payment_transactions (
        user_id,
        subscription_id,
        amount_cents,
        currency,
        status,
        transaction_type,
        payment_provider,
        payment_method,
        payment_reference,
        description,
        processed_at
    ) VALUES (
        p_user_id,
        v_subscription_id,
        400, -- $4.00
        'USD',
        'succeeded',
        'trial_conversion',
        p_payment_provider,
        p_payment_method,
        p_payment_reference,
        'Trial conversion to paid subscription',
        NOW()
    ) RETURNING id INTO v_transaction_id;
    
    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to renew subscription
CREATE OR REPLACE FUNCTION public.renew_subscription(
    p_user_id UUID,
    p_payment_reference TEXT,
    p_payment_provider TEXT
)
RETURNS UUID AS $$
DECLARE
    v_subscription_id UUID;
    v_period_end TIMESTAMP WITH TIME ZONE;
    v_transaction_id UUID;
BEGIN
    -- Get current active subscription
    SELECT id, current_period_end INTO v_subscription_id, v_period_end
    FROM public.user_subscriptions 
    WHERE user_id = p_user_id 
    AND status = 'active'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF v_subscription_id IS NULL THEN
        RAISE EXCEPTION 'No active subscription found for user';
    END IF;
    
    -- Extend subscription period
    UPDATE public.user_subscriptions 
    SET 
        current_period_start = v_period_end,
        current_period_end = v_period_end + INTERVAL '1 month',
        next_payment_due = v_period_end + INTERVAL '1 month',
        last_payment_date = NOW(),
        updated_at = NOW()
    WHERE id = v_subscription_id;
    
    -- Create payment transaction record
    INSERT INTO public.payment_transactions (
        user_id,
        subscription_id,
        amount_cents,
        currency,
        status,
        transaction_type,
        payment_provider,
        payment_reference,
        description,
        processed_at
    ) VALUES (
        p_user_id,
        v_subscription_id,
        400, -- $4.00
        'USD',
        'succeeded',
        'renewal',
        p_payment_provider,
        p_payment_reference,
        'Monthly subscription renewal',
        NOW()
    ) RETURNING id INTO v_transaction_id;
    
    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cancel subscription
CREATE OR REPLACE FUNCTION public.cancel_subscription(
    p_user_id UUID,
    p_cancel_immediately BOOLEAN DEFAULT false
)
RETURNS BOOLEAN AS $$
DECLARE
    v_subscription_id UUID;
BEGIN
    -- Get current subscription
    SELECT id INTO v_subscription_id
    FROM public.user_subscriptions 
    WHERE user_id = p_user_id 
    AND status IN ('trialing', 'active')
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF v_subscription_id IS NULL THEN
        RETURN false;
    END IF;
    
    IF p_cancel_immediately THEN
        -- Cancel immediately
        UPDATE public.user_subscriptions 
        SET 
            status = 'canceled',
            canceled_at = NOW(),
            current_period_end = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription_id;
    ELSE
        -- Cancel at period end
        UPDATE public.user_subscriptions 
        SET 
            cancel_at_period_end = true,
            canceled_at = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription_id;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically start trial for new users
CREATE OR REPLACE FUNCTION public.auto_start_trial()
RETURNS TRIGGER AS $$
BEGIN
    -- Start free trial for new user
    PERFORM public.start_free_trial(NEW.id);
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail user creation
        RAISE WARNING 'Failed to start trial for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-start trial for new users
CREATE TRIGGER auto_start_trial_trigger
    AFTER INSERT ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_start_trial();

COMMIT;
