-- Create fresh subscription system with 7-day trial and $4/month recurring payments
-- This migration creates all necessary tables, functions, and policies for the subscription system

BEGIN;

-- Create subscription plans table
CREATE TABLE public.subscription_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price_cents INTEGER NOT NULL, -- $4.00 = 400 cents
    currency TEXT DEFAULT 'USD',
    billing_interval TEXT DEFAULT 'month', -- month, year
    billing_interval_count INTEGER DEFAULT 1, -- every 1 month
    trial_period_days INTEGER DEFAULT 7,
    is_active BOOLEAN DEFAULT true,
    features JSON<PERSON> DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user subscriptions table
CREATE TABLE public.user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    plan_id UUID REFERENCES public.subscription_plans(id) ON DELETE RESTRICT NOT NULL,
    status TEXT CHECK (status IN ('trialing', 'active', 'past_due', 'canceled', 'unpaid')) DEFAULT 'trialing',
    
    -- Trial information
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    
    -- Subscription periods
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    
    -- Payment information
    next_payment_due TIMESTAMP WITH TIME ZONE,
    last_payment_date TIMESTAMP WITH TIME ZONE,
    payment_method TEXT, -- 'card', 'mobile_money', etc.
    payment_provider TEXT, -- 'stripe', 'paystack', 'flutterwave', etc.
    payment_reference TEXT, -- external subscription ID
    
    -- Cancellation
    canceled_at TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT false,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one active subscription per user
    UNIQUE(user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Create payment transactions table
CREATE TABLE public.payment_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    subscription_id UUID REFERENCES public.user_subscriptions(id) ON DELETE SET NULL,
    
    -- Transaction details
    amount_cents INTEGER NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded')) DEFAULT 'pending',
    transaction_type TEXT CHECK (transaction_type IN ('subscription', 'trial_conversion', 'renewal', 'refund')) DEFAULT 'subscription',
    
    -- Payment provider details
    payment_provider TEXT NOT NULL, -- 'stripe', 'paystack', 'flutterwave'
    payment_method TEXT, -- 'card', 'mobile_money', 'bank_transfer'
    payment_reference TEXT UNIQUE, -- external transaction ID
    provider_transaction_id TEXT,
    
    -- Additional data
    description TEXT,
    metadata JSONB DEFAULT '{}',
    processed_at TIMESTAMP WITH TIME ZONE,
    failed_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription usage tracking table
CREATE TABLE public.subscription_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    subscription_id UUID REFERENCES public.user_subscriptions(id) ON DELETE CASCADE,
    
    -- Usage metrics
    feature_name TEXT NOT NULL, -- 'quiz_generation', 'ai_tutor', 'pdf_extraction'
    usage_count INTEGER DEFAULT 0,
    usage_limit INTEGER, -- NULL means unlimited
    
    -- Period tracking
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, feature_name, period_start)
);

-- Insert default subscription plan
INSERT INTO public.subscription_plans (
    name,
    description,
    price_cents,
    currency,
    billing_interval,
    billing_interval_count,
    trial_period_days,
    features
) VALUES (
    'StudyFam Pro',
    'Full access to all StudyFam features including unlimited quiz generation, AI tutor, and advanced PDF extraction',
    400, -- $4.00
    'USD',
    'month',
    1,
    7,
    '["unlimited_quiz_generation", "ai_tutor_access", "advanced_pdf_extraction", "priority_support", "export_features"]'::jsonb
);

-- Create indexes for performance
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_trial_end ON public.user_subscriptions(trial_end);
CREATE INDEX idx_user_subscriptions_current_period_end ON public.user_subscriptions(current_period_end);
CREATE INDEX idx_payment_transactions_user_id ON public.payment_transactions(user_id);
CREATE INDEX idx_payment_transactions_subscription_id ON public.payment_transactions(subscription_id);
CREATE INDEX idx_payment_transactions_payment_reference ON public.payment_transactions(payment_reference);
CREATE INDEX idx_subscription_usage_user_id ON public.subscription_usage(user_id);
CREATE INDEX idx_subscription_usage_feature_period ON public.subscription_usage(user_id, feature_name, period_start);

-- Create updated_at triggers
CREATE TRIGGER subscription_plans_updated_at_trigger
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER user_subscriptions_updated_at_trigger
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER payment_transactions_updated_at_trigger
    BEFORE UPDATE ON public.payment_transactions
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER subscription_usage_updated_at_trigger
    BEFORE UPDATE ON public.subscription_usage
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

COMMIT;
