-- Comprehensive cleanup of payment and subscription system
-- This script removes all payment/subscription related tables, functions, and policies

-- Drop RLS policies first
DROP POLICY IF EXISTS "Anyone can view active subscription plans" ON public.subscription_plans;
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can view their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Users can insert their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Users can update their own transactions" ON public.payment_transactions;

-- Drop functions
DROP FUNCTION IF EXISTS public.start_free_trial(UUID, UUID);
DROP FUNCTION IF EXISTS public.check_subscription_status(UUID);
DROP FUNCTION IF EXISTS public.update_subscription_status();
DROP FUNCTION IF EXISTS public.handle_subscription_change();

-- Drop triggers
DROP TRIGGER IF EXISTS subscription_updated_at_trigger ON public.user_subscriptions;
DROP TRIGGER IF EXISTS subscription_plans_updated_at_trigger ON public.subscription_plans;
DROP TRIGGER IF EXISTS payment_transactions_updated_at_trigger ON public.payment_transactions;

-- Drop tables (in correct order due to foreign key constraints)
DROP TABLE IF EXISTS public.payment_transactions CASCADE;
DROP TABLE IF EXISTS public.user_subscriptions CASCADE;
DROP TABLE IF EXISTS public.subscription_plans CASCADE;
DROP TABLE IF EXISTS public.woocommerce_orders CASCADE;

-- Drop any remaining sequences
DROP SEQUENCE IF EXISTS subscription_plans_id_seq CASCADE;
DROP SEQUENCE IF EXISTS user_subscriptions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payment_transactions_id_seq CASCADE;

-- Drop any custom types related to payments
DROP TYPE IF EXISTS subscription_status CASCADE;
DROP TYPE IF EXISTS payment_status CASCADE;
DROP TYPE IF EXISTS transaction_type CASCADE;

-- Clean up any remaining indexes
DROP INDEX IF EXISTS idx_user_subscriptions_user_id;
DROP INDEX IF EXISTS idx_user_subscriptions_status;
DROP INDEX IF EXISTS idx_payment_transactions_user_id;
DROP INDEX IF EXISTS idx_payment_transactions_reference;
DROP INDEX IF EXISTS idx_subscription_plans_active;

-- Remove any views related to subscriptions
DROP VIEW IF EXISTS active_subscriptions CASCADE;
DROP VIEW IF EXISTS subscription_summary CASCADE;

COMMIT;
