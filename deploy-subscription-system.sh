#!/bin/bash

# Deploy Subscription System to Supabase
# This script deploys the subscription system migrations to Supabase

echo "🚀 Deploying StudyFam Subscription System..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Installing..."
    npm install -g supabase
fi

# Login to Supabase (if not already logged in)
echo "🔐 Checking Supabase authentication..."
supabase auth status || {
    echo "Please login to Supabase:"
    supabase login
}

# Link to project
echo "🔗 Linking to Supabase project..."
supabase link --project-ref umavpljptvamtmaygztq

# Apply migrations
echo "📊 Applying subscription system migrations..."

echo "  1/3 Creating subscription tables..."
supabase db push --file supabase/migrations/20250121000000_create_subscription_system.sql

echo "  2/3 Creating subscription functions..."
supabase db push --file supabase/migrations/20250121000001_subscription_functions.sql

echo "  3/3 Setting up RLS policies..."
supabase db push --file supabase/migrations/20250121000002_subscription_policies.sql

# Verify deployment
echo "✅ Verifying deployment..."
supabase db diff

echo "🎉 Subscription system deployed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Test the subscription system in your app"
echo "2. Configure payment providers (Stripe, PayStack, etc.)"
echo "3. Set up webhooks for payment processing"
echo "4. Monitor subscription metrics in Supabase dashboard"
echo ""
echo "💡 Features enabled:"
echo "  ✅ 7-day free trial for new users"
echo "  ✅ $4/month recurring subscriptions"
echo "  ✅ Automatic trial-to-paid conversion"
echo "  ✅ Subscription status tracking"
echo "  ✅ Payment transaction logging"
echo "  ✅ Usage analytics"
echo ""
echo "🔧 Manual deployment option:"
echo "If the CLI deployment fails, you can manually run these SQL files"
echo "in the Supabase SQL editor:"
echo "  - supabase/migrations/20250121000000_create_subscription_system.sql"
echo "  - supabase/migrations/20250121000001_subscription_functions.sql"
echo "  - supabase/migrations/20250121000002_subscription_policies.sql"
