-- Test Script for StudyFam Subscription System
-- Run this in Supabase SQL Editor to test the subscription system

-- Test 1: Check if tables exist
SELECT 'Testing table creation...' as test_step;

SELECT 
  table_name,
  CASE 
    WHEN table_name IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage') 
    THEN '✅ EXISTS' 
    ELSE '❌ MISSING' 
  END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage');

-- Test 2: Check if default plan exists
SELECT 'Testing default subscription plan...' as test_step;

SELECT 
  name,
  price_cents,
  currency,
  trial_period_days,
  is_active,
  CASE WHEN id IS NOT NULL THEN '✅ DEFAULT PLAN EXISTS' ELSE '❌ NO DEFAULT PLAN' END as status
FROM subscription_plans 
WHERE is_active = true;

-- Test 3: Check if functions exist
SELECT 'Testing subscription functions...' as test_step;

SELECT 
  routine_name,
  '✅ FUNCTION EXISTS' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
  'start_free_trial',
  'user_has_active_access',
  'get_user_subscription_status',
  'convert_trial_to_paid',
  'renew_subscription',
  'cancel_subscription'
);

-- Test 4: Check RLS policies
SELECT 'Testing RLS policies...' as test_step;

SELECT 
  tablename,
  policyname,
  '✅ POLICY EXISTS' as status
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage');

-- Test 5: Test trial creation (simulation)
SELECT 'Testing trial creation function...' as test_step;

-- This would normally be called with a real user ID
-- SELECT start_free_trial('00000000-0000-0000-0000-000000000000');

-- Test 6: Check indexes
SELECT 'Testing database indexes...' as test_step;

SELECT 
  indexname,
  tablename,
  '✅ INDEX EXISTS' as status
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('user_subscriptions', 'payment_transactions', 'subscription_usage')
AND indexname LIKE 'idx_%';

-- Test 7: Check triggers
SELECT 'Testing database triggers...' as test_step;

SELECT 
  trigger_name,
  event_object_table,
  '✅ TRIGGER EXISTS' as status
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND event_object_table IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage');

-- Summary Report
SELECT 'SUBSCRIPTION SYSTEM TEST SUMMARY' as summary;

SELECT 
  'Tables' as component,
  COUNT(*) as count,
  CASE WHEN COUNT(*) = 4 THEN '✅ ALL GOOD' ELSE '❌ MISSING TABLES' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage')

UNION ALL

SELECT 
  'Functions' as component,
  COUNT(*) as count,
  CASE WHEN COUNT(*) >= 6 THEN '✅ ALL GOOD' ELSE '❌ MISSING FUNCTIONS' END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
  'start_free_trial',
  'user_has_active_access', 
  'get_user_subscription_status',
  'convert_trial_to_paid',
  'renew_subscription',
  'cancel_subscription'
)

UNION ALL

SELECT 
  'Default Plan' as component,
  COUNT(*) as count,
  CASE WHEN COUNT(*) >= 1 THEN '✅ ALL GOOD' ELSE '❌ NO DEFAULT PLAN' END as status
FROM subscription_plans 
WHERE is_active = true

UNION ALL

SELECT 
  'RLS Policies' as component,
  COUNT(*) as count,
  CASE WHEN COUNT(*) >= 8 THEN '✅ ALL GOOD' ELSE '❌ MISSING POLICIES' END as status
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage');

-- Test subscription flow simulation
SELECT 'TESTING SUBSCRIPTION FLOW SIMULATION' as flow_test;

-- Show what a typical subscription flow would look like
SELECT 
  'Step 1: User registers → Auto trial starts' as step,
  'start_free_trial(user_id) called by trigger' as action,
  '7 days free access' as result

UNION ALL

SELECT 
  'Step 2: User uses features' as step,
  'user_has_active_access(user_id) returns true' as action,
  'Full access granted' as result

UNION ALL

SELECT 
  'Step 3: Trial expires' as step,
  'user_has_active_access(user_id) returns false' as action,
  'Subscription wall appears' as result

UNION ALL

SELECT 
  'Step 4: User pays $4' as step,
  'convert_trial_to_paid(user_id, payment_ref, provider, method)' as action,
  '30 days paid access' as result

UNION ALL

SELECT 
  'Step 5: Monthly renewal' as step,
  'renew_subscription(user_id, payment_ref, provider)' as action,
  'Another 30 days access' as result;

-- Final status check
SELECT 
  CASE 
    WHEN (
      SELECT COUNT(*) FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('subscription_plans', 'user_subscriptions', 'payment_transactions', 'subscription_usage')
    ) = 4
    AND (
      SELECT COUNT(*) FROM subscription_plans WHERE is_active = true
    ) >= 1
    AND (
      SELECT COUNT(*) FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name IN ('start_free_trial', 'user_has_active_access', 'get_user_subscription_status')
    ) >= 3
    THEN '🎉 SUBSCRIPTION SYSTEM READY FOR PRODUCTION!'
    ELSE '⚠️ SUBSCRIPTION SYSTEM NEEDS ATTENTION'
  END as final_status;
