-- PAYMENT SYSTEM CLEANUP SCRIPT
-- Copy and paste this entire script into your Supabase SQL Editor
-- This will completely remove all payment and subscription related data

-- First, let's see what tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (table_name LIKE '%subscription%' OR table_name LIKE '%payment%' OR table_name LIKE '%woocommerce%');

-- Drop all payment/subscription related policies
DO $$ 
BEGIN
    -- Drop policies if they exist
    DROP POLICY IF EXISTS "Anyone can view active subscription plans" ON public.subscription_plans;
    DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.user_subscriptions;
    DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON public.user_subscriptions;
    DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.user_subscriptions;
    DROP POLICY IF EXISTS "Users can view their own transactions" ON public.payment_transactions;
    DROP POLICY IF EXISTS "Users can insert their own transactions" ON public.payment_transactions;
    DROP POLICY IF EXISTS "Service role can manage subscriptions" ON public.user_subscriptions;
    DROP POLICY IF EXISTS "Service role can manage transactions" ON public.payment_transactions;
    
    RAISE NOTICE 'Dropped all payment-related policies';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Some policies may not have existed: %', SQLERRM;
END $$;

-- Drop all payment/subscription related functions
DO $$ 
BEGIN
    DROP FUNCTION IF EXISTS public.start_free_trial(UUID, UUID);
    DROP FUNCTION IF EXISTS public.check_subscription_status(UUID);
    DROP FUNCTION IF EXISTS public.update_subscription_status();
    DROP FUNCTION IF EXISTS public.handle_subscription_change();
    DROP FUNCTION IF EXISTS public.get_user_subscription_status(UUID);
    DROP FUNCTION IF EXISTS public.activate_subscription(UUID, UUID);
    
    RAISE NOTICE 'Dropped all payment-related functions';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Some functions may not have existed: %', SQLERRM;
END $$;

-- Drop all payment/subscription related tables
DO $$ 
BEGIN
    DROP TABLE IF EXISTS public.payment_transactions CASCADE;
    DROP TABLE IF EXISTS public.user_subscriptions CASCADE;
    DROP TABLE IF EXISTS public.subscription_plans CASCADE;
    DROP TABLE IF EXISTS public.woocommerce_orders CASCADE;
    
    RAISE NOTICE 'Dropped all payment-related tables';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Some tables may not have existed: %', SQLERRM;
END $$;

-- Verify cleanup
SELECT 'Cleanup completed successfully!' as status;

-- Show remaining tables to confirm
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (table_name LIKE '%subscription%' OR table_name LIKE '%payment%' OR table_name LIKE '%woocommerce%');
