# Deploy PDF Extraction Edge Function to Supabase

## Quick Deployment Guide

### Option 1: Using Supabase Dashboard (Recommended)

1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Navigate to your project: `umavpljptvamtmaygztq`

2. **Access Edge Functions**
   - Click on "Edge Functions" in the left sidebar
   - Click "Create a new function"

3. **Create the Function**
   - Function name: `extract-pdf-text`
   - Copy the content from `supabase/functions/extract-pdf-text/index.ts`
   - Paste it into the function editor
   - Click "Deploy function"

### Option 2: Using Supabase CLI

```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref umavpljptvamtmaygztq

# Deploy the specific function
supabase functions deploy extract-pdf-text

# Or deploy all functions
supabase functions deploy
```

### Option 3: Manual Deployment via API

```bash
# Get your access token from Supabase dashboard
# Then use curl to deploy

curl -X POST 'https://api.supabase.com/v1/projects/umavpljptvamtmaygztq/functions' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "slug": "extract-pdf-text",
    "name": "extract-pdf-text",
    "source": "BASE64_ENCODED_FUNCTION_CODE"
  }'
```

## Function Details

- **Function Name**: `extract-pdf-text`
- **Purpose**: Advanced PDF text extraction using PDF.js
- **Features**:
  - Multiple extraction methods (Advanced, Basic, Binary)
  - Intelligent text positioning
  - Support for up to 200 pages
  - Comprehensive error handling
  - Progress tracking

## Testing the Function

After deployment, test with:

```javascript
const { data, error } = await supabase.functions.invoke('extract-pdf-text', {
  body: formData, // FormData with PDF file
  headers: {
    Authorization: `Bearer ${session.access_token}`,
  },
});
```

## Troubleshooting

### Common Issues:

1. **Function not found**: Ensure the function is deployed and named correctly
2. **CORS errors**: The function includes CORS headers
3. **Large file errors**: Function supports up to 10MB files
4. **Authentication errors**: Ensure user is logged in

### Fallback Strategy:

The app automatically falls back to client-side extraction if the Edge Function fails, so the PDF extraction will still work even if the function isn't deployed.

## Current Status

✅ **Client-side extraction**: Enhanced with 5 extraction methods
✅ **Hybrid extraction**: Server-side + client-side fallback
✅ **Quality analysis**: Advanced text enhancement and validation
⚠️ **Edge Function**: Needs to be deployed to Supabase

## Next Steps

1. Deploy the Edge Function using one of the methods above
2. Test PDF extraction with various document types
3. Monitor function performance in Supabase dashboard
4. Adjust extraction parameters if needed
